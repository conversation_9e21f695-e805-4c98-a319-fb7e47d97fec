<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.videoringtoneapp"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- Additional permissions required for dialer app -->
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="android.permission.ADD_VOICEMAIL" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.CONTROL_INCALL_EXPERIENCE" />
    <uses-permission android:name="android.permission.BIND_INCALL_SERVICE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <permission
        android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.videoringtoneapp.MainApplication"
        android:allowBackup="false"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme" >
        <activity
            android:name="com.videoringtoneapp.MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Dialer Activity for handling dialer intents -->
        <activity
            android:name="com.videoringtoneapp.DialerActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.NoDisplay" >

            <!-- Handle dialer intents -->
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.DIAL" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.DIAL" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="tel" />
            </intent-filter>
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.CALL_PRIVILEGED" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="tel" />
            </intent-filter>
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="tel" />
            </intent-filter>
        </activity>

        <!-- InCallService - REQUIRED for default dialer -->
        <service
            android:name="com.videoringtoneapp.VideoRingtoneInCallService"
            android:exported="true"
            android:permission="android.permission.BIND_INCALL_SERVICE" >
            <intent-filter>
                <action android:name="android.telecom.InCallService" />
            </intent-filter>

            <meta-data
                android:name="android.telecom.INCLUDE_EXTERNAL_CALLS"
                android:value="true" />
            <meta-data
                android:name="android.telecom.INCLUDE_SELF_MANAGED_CALLS"
                android:value="true" />
        </service>

        <!-- Video Ringtone Service -->
        <service
            android:name="com.videoringtoneapp.VideoRingtoneService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall" />

        <!-- Call State Receiver -->
        <receiver
            android:name="com.videoringtoneapp.CallStateReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.PHONE_STATE" />
            </intent-filter>
        </receiver>

        <!-- Video Ringtone Activity -->
        <activity
            android:name="com.videoringtoneapp.VideoRingtoneActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTop"
            android:showOnLockScreen="true"
            android:theme="@style/VideoRingtoneTheme"
            android:turnScreenOn="true" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.videoringtoneapp.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.facebook.soloader.enabled"
            android:value="false" />
    </application>

</manifest>