package com.videoringtoneapp

import android.content.Intent
import android.telecom.Call
import android.telecom.InCallService
import android.util.Log

class VideoRingtoneInCallService : InCallService() {
    
    companion object {
        private const val TAG = "VideoRingtoneInCallService"
    }

    private val callCallback = object : Call.Callback() {
        override fun onStateChanged(call: Call, state: Int) {
            super.onStateChanged(call, state)
            Log.d(TAG, "Call state changed: $state")
            
            when (state) {
                Call.STATE_RINGING -> {
                    Log.d(TAG, "Incoming call detected")
                    handleIncomingCall(call)
                }
                Call.STATE_ACTIVE -> {
                    Log.d(TAG, "Call answered")
                    stopVideoRingtone()
                }
                Call.STATE_DISCONNECTED -> {
                    Log.d(TAG, "Call ended")
                    stopVideoRingtone()
                }
            }
        }
    }

    override fun onCallAdded(call: Call) {
        super.onCallAdded(call)
        Log.d(TAG, "Call added: ${call.details}")
        call.registerCallback(callCallback)
        
        if (call.state == Call.STATE_RINGING) {
            handleIncomingCall(call)
        }
    }

    override fun onCallRemoved(call: Call) {
        super.onCallRemoved(call)
        Log.d(TAG, "Call removed")
        call.unregisterCallback(callCallback)
        stopVideoRingtone()
    }

    private fun handleIncomingCall(call: Call) {
        try {
            val phoneNumber = call.details.handle?.schemeSpecificPart ?: "Unknown"
            Log.d(TAG, "Handling incoming call from: $phoneNumber")
            
            // Start video ringtone service
            val serviceIntent = Intent(this, VideoRingtoneService::class.java).apply {
                action = VideoRingtoneService.ACTION_START_VIDEO_RINGTONE
                putExtra("phone_number", phoneNumber)
            }
            startForegroundService(serviceIntent)
            
            // Start video ringtone activity
            val activityIntent = Intent(this, VideoRingtoneActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or 
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP
                putExtra("phone_number", phoneNumber)
            }
            startActivity(activityIntent)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling incoming call", e)
        }
    }

    private fun stopVideoRingtone() {
        try {
            // Stop video ringtone service
            val serviceIntent = Intent(this, VideoRingtoneService::class.java).apply {
                action = VideoRingtoneService.ACTION_STOP_VIDEO_RINGTONE
            }
            startService(serviceIntent)
            
            // Finish video ringtone activity
            val activityIntent = Intent(this, VideoRingtoneActivity::class.java).apply {
                action = VideoRingtoneActivity.ACTION_FINISH_ACTIVITY
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(activityIntent)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping video ringtone", e)
        }
    }
}
