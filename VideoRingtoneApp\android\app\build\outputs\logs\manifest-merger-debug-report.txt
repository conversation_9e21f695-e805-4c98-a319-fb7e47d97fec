-- Merging decision tree log ---
manifest
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:1:1-137:12
MERGED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:1:1-137:12
INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-permissions] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-video] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-fs] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be7db4ecb26cb7015d85f3a4877ac79\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2455bfab1cfa3eca9fababdaf610ea7\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\05a33f2d6bd7be7919e7340db7ec1ea5\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\651bce247f4f9f903fb00b82c8c5fc74\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d3cd8eb09d66558d60c55d9fb7ffc71\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc2837e137cfd2be406328534667258a\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af07a135a4ef7c0df946877d933b0dcc\transformed\media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfda21954fd43dece074a90fbf362b2b\transformed\media3-session-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4e060919cc853080eb30399a936ff\transformed\media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\886a35d36f733f3cf458d79a54f3825b\transformed\media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b8d44e6b460e14f5d10365998e06497\transformed\media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4293066f83427f850ed7f59c3a493ac0\transformed\media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ec2824b6d2184bedd0579ac9b600f57\transformed\media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\011c4cf88c76d6ab626997eb92d9d4bb\transformed\media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\527d9fee6bec424b9f5cfd79fee94831\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\425a5bd35d4a2b4cf3e11607bdea0cae\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf011c1fc31440de40f8de1f0ce09ca\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d302cce779d38cf0905d2b85271d3a70\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52356d6d90573e7f06600304672dd531\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10647f93e63935c4eeff6f8401d54491\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d99e7495a573fcae209fe6c0105115\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0ec3c8efa8a6ba694f451d596623fb2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cec94702adb61b4c45d094e0e7b69e22\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e664fb735a92e9bf7e81f06fa1043c9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c1547cb4a3cfae9907fdc8ea27e52b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b609e350da004f62b4d5503f37f80b2\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b38f9013889be0f80a2753f9f62b651\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5399fcee990f8133b395557f23dead90\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86be6069ec79834ce260fb6a07e53fb7\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4451ab8f99cea2c5d21bdd4512797e0\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b2ead8e1bb1acdd45d77172c05f3e09\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff749c6428e9e054022b0e134591e49e\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\532abdfe791d40636e3d0d3c21a6ea5e\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\22e70e8a865443220297a9bb645ad1fc\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5c6c8dbe07e136d6eccf1241a9c1cde\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb056705a896f82f4fd713a0ce78acb7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd06847a50857d90aa16208147a4d0f2\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1847b2f34ce051b5b53aa6c2626e6430\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\393e51f598ac341bf98e48a73726e5a5\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4fb3153722752c26c207bac48a74857\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\498a8e79bf35af451e9f90ea8fd91465\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ccb0d640caf4398d507a0622bf39c5f\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e641725f61e4a7d4f1565785ffb49bb6\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b46b669c2d945d2fda517da130e62f0a\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b48f0baccc9e1df24529bede4282ae\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\949e4d546c2efc5f4439f65dd1d74b2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bf305a4c1ea896d0383b0a33f9de775\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c234a4120de6712f0fe3c983ec01535\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\75c1fa56d9c0eec39556d8ccddfd61c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c89f45b69dfc7add7372db9e2dfaf4f2\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\74de6f84e85601b7d879e8bc3606e3fe\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe5fbf2655f83e1c70fc1c395b6dbbf2\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\138c1389f563f6539af20532c4e81b5f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8c1d43c6bd4bfd94f802c20e2f34ed6\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7e60fef2effa48413b5e5fe8011fbb6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f778ade8f7cf2920224dc4f88ce4c50\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c7d1e59ed007f0a5857e26b2d1d6cc8\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91a94c10768de6e7e46fcdf99215bf3\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fdf5de313bf0c1841406b8387478bc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3d313c6b9bf7d2e7d6ae73d4bab07e3\transformed\tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\109fe72c2b01c5dc31640cea183b9745\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ccbae0f41e442baf2e59eea5c738174\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0359dc3e4beba4c31dab86cb5151fe11\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ee5b28c5a7092e8c10963bb6da395a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d96d6c5d6dac6a2393c316abecbbaff2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72cde7dc85b5006383f56c98fcfedfa5\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
	package
		INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:5-67
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:22-64
uses-permission#android.permission.READ_PHONE_STATE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:5-75
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:22-72
uses-permission#android.permission.CALL_PHONE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:5-69
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:22-66
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:5-77
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:22-74
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:22-75
uses-permission#android.permission.WAKE_LOCK
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:5-68
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:22-65
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:5-75
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:22-72
uses-permission#android.permission.VIBRATE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:5-66
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:22-63
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:5-80
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:5-81
MERGED from [:react-native-fs] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:react-native-fs] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:22-78
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:5-75
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:22-72
uses-permission#android.permission.READ_CONTACTS
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:5-72
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:22-69
uses-permission#android.permission.WRITE_CONTACTS
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:5-73
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:22-70
uses-permission#android.permission.READ_CALL_LOG
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:5-72
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:22-69
uses-permission#android.permission.WRITE_CALL_LOG
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:5-73
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:22-70
uses-permission#android.permission.ADD_VOICEMAIL
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:5-72
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:22-69
uses-permission#android.permission.MODIFY_PHONE_STATE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:5-77
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:22-74
uses-permission#android.permission.CONTROL_INCALL_EXPERIENCE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:5-84
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:22-81
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:5-111
	tools:ignore
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:80-108
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:22-79
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:5-77
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_PHONE_CALL
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:5-88
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:22-85
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:5-81
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:22-78
application
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:29:5-136:19
MERGED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:29:5-136:19
MERGED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:29:5-136:19
INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fdf5de313bf0c1841406b8387478bc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fdf5de313bf0c1841406b8387478bc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:36:7-33
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:36:7-33
	android:label
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:31:7-39
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:31:7-39
	tools:ignore
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:33:7-52
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:33:7-52
	tools:targetApi
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:32:7-41
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:32:7-41
	android:allowBackup
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:34:7-34
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:34:7-34
	android:theme
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:35:7-38
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:35:7-38
	android:usesCleartextTraffic
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:6:9-44
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:30:7-38
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:30:7-38
activity#com.videoringtoneapp.MainActivity
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:37:7-74:18
	android:label
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:39:9-41
	android:launchMode
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:41:9-40
	android:windowSoftInputMode
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:42:9-51
	android:exported
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:43:9-32
	android:configChanges
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:40:9-118
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:38:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:44:9-47:25
action#android.intent.action.MAIN
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:13-65
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:21-62
category#android.intent.category.LAUNCHER
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:13-73
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:23-70
intent-filter#action:name:android.intent.action.DIAL+category:name:android.intent.category.DEFAULT
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:50:9-53:25
action#android.intent.action.DIAL
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
category#android.intent.category.DEFAULT
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
intent-filter#action:name:android.intent.action.DIAL+category:name:android.intent.category.DEFAULT+data:scheme:tel
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:55:9-59:25
data
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
	android:scheme
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
intent-filter#action:name:android.intent.action.CALL_PRIVILEGED+category:name:android.intent.category.DEFAULT+data:scheme:tel
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:9-65:25
action#android.intent.action.CALL_PRIVILEGED
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-76
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:21-73
intent-filter#action:name:android.intent.action.DIAL+action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:tel
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:67:9-73:25
action#android.intent.action.VIEW
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-65
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:21-62
category#android.intent.category.BROWSABLE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:13-74
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:23-71
activity#com.videoringtoneapp.DialerActivity
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:77:7-108:18
	android:label
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:79:9-41
	android:excludeFromRecents
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:81:9-42
	android:exported
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:82:9-32
	android:theme
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:80:9-55
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:78:9-39
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:tel
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:9-107:25
	android:priority
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:24-47
service#com.videoringtoneapp.VideoRingtoneService
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:111:7-115:53
	android:enabled
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:113:9-31
	android:exported
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:114:9-33
	android:foregroundServiceType
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:115:9-50
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:112:9-45
receiver#com.videoringtoneapp.CallStateReceiver
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:118:7-125:18
	android:enabled
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:120:9-31
	android:exported
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:121:9-32
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:119:9-42
intent-filter#action:name:android.intent.action.PHONE_STATE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:9-124:25
	android:priority
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:24-47
action#android.intent.action.PHONE_STATE
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:11-70
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:19-67
activity#com.videoringtoneapp.VideoRingtoneActivity
ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:128:7-135:36
	android:turnScreenOn
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:134:9-36
	android:excludeFromRecents
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:132:9-42
	android:launchMode
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:131:9-39
	android:exported
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:135:9-33
	android:showOnLockScreen
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:133:9-40
	android:theme
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:130:9-50
	android:name
		ADDED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:129:9-46
uses-sdk
INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-permissions] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-permissions] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-permissions\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-video] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-video\android\buildOutput_a15d4dee7fc4eda61b91308cbb6a2e72\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-fs] H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\node_modules\react-native-fs\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be7db4ecb26cb7015d85f3a4877ac79\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be7db4ecb26cb7015d85f3a4877ac79\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2455bfab1cfa3eca9fababdaf610ea7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2455bfab1cfa3eca9fababdaf610ea7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\05a33f2d6bd7be7919e7340db7ec1ea5\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\05a33f2d6bd7be7919e7340db7ec1ea5\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\651bce247f4f9f903fb00b82c8c5fc74\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\651bce247f4f9f903fb00b82c8c5fc74\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d3cd8eb09d66558d60c55d9fb7ffc71\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d3cd8eb09d66558d60c55d9fb7ffc71\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc2837e137cfd2be406328534667258a\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc2837e137cfd2be406328534667258a\transformed\media3-datasource-okhttp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af07a135a4ef7c0df946877d933b0dcc\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\af07a135a4ef7c0df946877d933b0dcc\transformed\media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfda21954fd43dece074a90fbf362b2b\transformed\media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dfda21954fd43dece074a90fbf362b2b\transformed\media3-session-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4e060919cc853080eb30399a936ff\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcd4e060919cc853080eb30399a936ff\transformed\media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\886a35d36f733f3cf458d79a54f3825b\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\886a35d36f733f3cf458d79a54f3825b\transformed\media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b8d44e6b460e14f5d10365998e06497\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b8d44e6b460e14f5d10365998e06497\transformed\media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4293066f83427f850ed7f59c3a493ac0\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4293066f83427f850ed7f59c3a493ac0\transformed\media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ec2824b6d2184bedd0579ac9b600f57\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ec2824b6d2184bedd0579ac9b600f57\transformed\media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\011c4cf88c76d6ab626997eb92d9d4bb\transformed\media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\011c4cf88c76d6ab626997eb92d9d4bb\transformed\media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\527d9fee6bec424b9f5cfd79fee94831\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\527d9fee6bec424b9f5cfd79fee94831\transformed\media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\425a5bd35d4a2b4cf3e11607bdea0cae\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\425a5bd35d4a2b4cf3e11607bdea0cae\transformed\media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf011c1fc31440de40f8de1f0ce09ca\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf011c1fc31440de40f8de1f0ce09ca\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d302cce779d38cf0905d2b85271d3a70\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d302cce779d38cf0905d2b85271d3a70\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52356d6d90573e7f06600304672dd531\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52356d6d90573e7f06600304672dd531\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10647f93e63935c4eeff6f8401d54491\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10647f93e63935c4eeff6f8401d54491\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d99e7495a573fcae209fe6c0105115\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d99e7495a573fcae209fe6c0105115\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0ec3c8efa8a6ba694f451d596623fb2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0ec3c8efa8a6ba694f451d596623fb2\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cec94702adb61b4c45d094e0e7b69e22\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cec94702adb61b4c45d094e0e7b69e22\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e664fb735a92e9bf7e81f06fa1043c9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e664fb735a92e9bf7e81f06fa1043c9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c1547cb4a3cfae9907fdc8ea27e52b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c1547cb4a3cfae9907fdc8ea27e52b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b609e350da004f62b4d5503f37f80b2\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b609e350da004f62b4d5503f37f80b2\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b38f9013889be0f80a2753f9f62b651\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b38f9013889be0f80a2753f9f62b651\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5399fcee990f8133b395557f23dead90\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5399fcee990f8133b395557f23dead90\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86be6069ec79834ce260fb6a07e53fb7\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86be6069ec79834ce260fb6a07e53fb7\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4451ab8f99cea2c5d21bdd4512797e0\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d4451ab8f99cea2c5d21bdd4512797e0\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b2ead8e1bb1acdd45d77172c05f3e09\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b2ead8e1bb1acdd45d77172c05f3e09\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff749c6428e9e054022b0e134591e49e\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff749c6428e9e054022b0e134591e49e\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\532abdfe791d40636e3d0d3c21a6ea5e\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\532abdfe791d40636e3d0d3c21a6ea5e\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\22e70e8a865443220297a9bb645ad1fc\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\22e70e8a865443220297a9bb645ad1fc\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5c6c8dbe07e136d6eccf1241a9c1cde\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b5c6c8dbe07e136d6eccf1241a9c1cde\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb056705a896f82f4fd713a0ce78acb7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb056705a896f82f4fd713a0ce78acb7\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd06847a50857d90aa16208147a4d0f2\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd06847a50857d90aa16208147a4d0f2\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1847b2f34ce051b5b53aa6c2626e6430\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1847b2f34ce051b5b53aa6c2626e6430\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\393e51f598ac341bf98e48a73726e5a5\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\393e51f598ac341bf98e48a73726e5a5\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4fb3153722752c26c207bac48a74857\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4fb3153722752c26c207bac48a74857\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\498a8e79bf35af451e9f90ea8fd91465\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\498a8e79bf35af451e9f90ea8fd91465\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ccb0d640caf4398d507a0622bf39c5f\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ccb0d640caf4398d507a0622bf39c5f\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e641725f61e4a7d4f1565785ffb49bb6\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e641725f61e4a7d4f1565785ffb49bb6\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b46b669c2d945d2fda517da130e62f0a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b46b669c2d945d2fda517da130e62f0a\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b48f0baccc9e1df24529bede4282ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\51b48f0baccc9e1df24529bede4282ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\949e4d546c2efc5f4439f65dd1d74b2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\949e4d546c2efc5f4439f65dd1d74b2b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bf305a4c1ea896d0383b0a33f9de775\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6bf305a4c1ea896d0383b0a33f9de775\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c234a4120de6712f0fe3c983ec01535\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c234a4120de6712f0fe3c983ec01535\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\75c1fa56d9c0eec39556d8ccddfd61c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\75c1fa56d9c0eec39556d8ccddfd61c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c89f45b69dfc7add7372db9e2dfaf4f2\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c89f45b69dfc7add7372db9e2dfaf4f2\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\74de6f84e85601b7d879e8bc3606e3fe\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\74de6f84e85601b7d879e8bc3606e3fe\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe5fbf2655f83e1c70fc1c395b6dbbf2\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe5fbf2655f83e1c70fc1c395b6dbbf2\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\138c1389f563f6539af20532c4e81b5f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\138c1389f563f6539af20532c4e81b5f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8c1d43c6bd4bfd94f802c20e2f34ed6\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8c1d43c6bd4bfd94f802c20e2f34ed6\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7e60fef2effa48413b5e5fe8011fbb6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d7e60fef2effa48413b5e5fe8011fbb6\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f778ade8f7cf2920224dc4f88ce4c50\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f778ade8f7cf2920224dc4f88ce4c50\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c7d1e59ed007f0a5857e26b2d1d6cc8\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c7d1e59ed007f0a5857e26b2d1d6cc8\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91a94c10768de6e7e46fcdf99215bf3\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b91a94c10768de6e7e46fcdf99215bf3\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fdf5de313bf0c1841406b8387478bc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fdf5de313bf0c1841406b8387478bc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3d313c6b9bf7d2e7d6ae73d4bab07e3\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3d313c6b9bf7d2e7d6ae73d4bab07e3\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\109fe72c2b01c5dc31640cea183b9745\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\109fe72c2b01c5dc31640cea183b9745\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ccbae0f41e442baf2e59eea5c738174\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ccbae0f41e442baf2e59eea5c738174\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0359dc3e4beba4c31dab86cb5151fe11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0359dc3e4beba4c31dab86cb5151fe11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ee5b28c5a7092e8c10963bb6da395a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ee5b28c5a7092e8c10963bb6da395a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d96d6c5d6dac6a2393c316abecbbaff2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d96d6c5d6dac6a2393c316abecbbaff2\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72cde7dc85b5006383f56c98fcfedfa5\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72cde7dc85b5006383f56c98fcfedfa5\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf011c1fc31440de40f8de1f0ce09ca\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\edf011c1fc31440de40f8de1f0ce09ca\transformed\media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:22-76
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fdf5de313bf0c1841406b8387478bc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f9fdf5de313bf0c1841406b8387478bc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
