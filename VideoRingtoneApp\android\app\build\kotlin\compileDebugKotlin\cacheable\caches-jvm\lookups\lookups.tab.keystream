  Manifest android  ANSWER_PHONE_CALLS android.Manifest.permission  
CALL_PHONE android.Manifest.permission  READ_PHONE_STATE android.Manifest.permission  
ic_media_play android.R.drawable  ic_menu_call android.R.drawable  Application android.app  KeyguardManager android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  ACTION_FINISH_ACTIVITY android.app.Activity  Build android.app.Activity  Button android.app.Activity  Context android.app.Activity  DefaultReactActivityDelegate android.app.Activity  	Exception android.app.Activity  LinearLayout android.app.Activity  Log android.app.Activity  MediaPlayer android.app.Activity  PowerManager android.app.Activity  Suppress android.app.Activity  TAG android.app.Activity  TelecomManager android.app.Activity  TextView android.app.Activity  Uri android.app.Activity  	VideoView android.app.Activity  View android.app.Activity  
WindowManager android.app.Activity  android android.app.Activity  
answerCall android.app.Activity  apply android.app.Activity  declineCall android.app.Activity  
fabricEnabled android.app.Activity  finish android.app.Activity  finishActivity android.app.Activity  getSystemService android.app.Activity  intent android.app.Activity  
isInitialized android.app.Activity  let android.app.Activity  onCreate android.app.Activity  onNewIntent android.app.Activity  phoneNumber android.app.Activity  setShowWhenLocked android.app.Activity  setTurnScreenOn android.app.Activity  window android.app.Activity  Boolean android.app.Application  BuildConfig android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  OpenSourceMergedSoMapping android.app.Application  PackageList android.app.Application  ReactPackage android.app.Application  SoLoader android.app.Application  String android.app.Application  VideoRingtonePackage android.app.Application  apply android.app.Application  getDefaultReactHost android.app.Application  load android.app.Application  onCreate android.app.Application  apply android.app.NotificationChannel  description android.app.NotificationChannel  setSound android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  ACTION_START_VIDEO_RINGTONE android.app.Service  ACTION_STOP_VIDEO_RINGTONE android.app.Service  AudioAttributes android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  	Exception android.app.Service  Intent android.app.Service  Log android.app.Service  MainActivity android.app.Service  MediaPlayer android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  PowerManager android.app.Service  RingtoneManager android.app.Service  START_NOT_STICKY android.app.Service  Suppress android.app.Service  TAG android.app.Service  Vibrator android.app.Service  android android.app.Service  apply android.app.Service  java android.app.Service  let android.app.Service  longArrayOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  startForeground android.app.Service  stopForeground android.app.Service  stopSelf android.app.Service  RoleManager android.app.role  ROLE_DIALER android.app.role.RoleManager  createRequestRoleIntent android.app.role.RoleManager  
isRoleHeld android.app.role.RoleManager  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  	Exception !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  TelephonyManager !android.content.BroadcastReceiver  VideoRingtoneActivity !android.content.BroadcastReceiver  VideoRingtoneService !android.content.BroadcastReceiver  apply !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  ACTION_FINISH_ACTIVITY android.content.Context  ACTION_START_VIDEO_RINGTONE android.content.Context  ACTION_STOP_VIDEO_RINGTONE android.content.Context  AudioAttributes android.content.Context  Boolean android.content.Context  Build android.content.Context  BuildConfig android.content.Context  Button android.content.Context  
CHANNEL_ID android.content.Context  Context android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  	Exception android.content.Context  Intent android.content.Context  LinearLayout android.content.Context  List android.content.Context  Log android.content.Context  MainActivity android.content.Context  MediaPlayer android.content.Context  NOTIFICATION_ID android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  OpenSourceMergedSoMapping android.content.Context  
POWER_SERVICE android.content.Context  PackageList android.content.Context  
PendingIntent android.content.Context  PowerManager android.content.Context  ROLE_SERVICE android.content.Context  ReactPackage android.content.Context  RingtoneManager android.content.Context  START_NOT_STICKY android.content.Context  SoLoader android.content.Context  String android.content.Context  Suppress android.content.Context  TAG android.content.Context  TELECOM_SERVICE android.content.Context  TelecomManager android.content.Context  TextView android.content.Context  Uri android.content.Context  VIBRATOR_SERVICE android.content.Context  Vibrator android.content.Context  VideoRingtonePackage android.content.Context  	VideoView android.content.Context  View android.content.Context  
WindowManager android.content.Context  android android.content.Context  
answerCall android.content.Context  apply android.content.Context  declineCall android.content.Context  
fabricEnabled android.content.Context  getDefaultReactHost android.content.Context  getSystemService android.content.Context  
isInitialized android.content.Context  java android.content.Context  let android.content.Context  load android.content.Context  longArrayOf android.content.Context  phoneNumber android.content.Context  
startActivity android.content.Context  startForegroundService android.content.Context  startService android.content.Context  ACTION_FINISH_ACTIVITY android.content.ContextWrapper  ACTION_START_VIDEO_RINGTONE android.content.ContextWrapper  ACTION_STOP_VIDEO_RINGTONE android.content.ContextWrapper  AudioAttributes android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  Button android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  Context android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  	Exception android.content.ContextWrapper  Intent android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  List android.content.ContextWrapper  Log android.content.ContextWrapper  MainActivity android.content.ContextWrapper  MediaPlayer android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  OpenSourceMergedSoMapping android.content.ContextWrapper  PackageList android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PowerManager android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  RingtoneManager android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  SoLoader android.content.ContextWrapper  String android.content.ContextWrapper  Suppress android.content.ContextWrapper  TAG android.content.ContextWrapper  TelecomManager android.content.ContextWrapper  TextView android.content.ContextWrapper  Uri android.content.ContextWrapper  Vibrator android.content.ContextWrapper  VideoRingtonePackage android.content.ContextWrapper  	VideoView android.content.ContextWrapper  View android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  android android.content.ContextWrapper  
answerCall android.content.ContextWrapper  applicationContext android.content.ContextWrapper  applicationInfo android.content.ContextWrapper  apply android.content.ContextWrapper  declineCall android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  getDefaultReactHost android.content.ContextWrapper  getSystemService android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  java android.content.ContextWrapper  let android.content.ContextWrapper  load android.content.ContextWrapper  longArrayOf android.content.ContextWrapper  packageManager android.content.ContextWrapper  packageName android.content.ContextWrapper  phoneNumber android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startService android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_ACTIVITY_SINGLE_TOP android.content.Intent  Intent android.content.Intent  TelecomManager android.content.Intent  Uri android.content.Intent  VideoRingtoneActivity android.content.Intent  VideoRingtoneService android.content.Intent  action android.content.Intent  apply android.content.Intent  data android.content.Intent  flags android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  reactApplicationContext android.content.Intent  PackageInfo android.content.pm  PackageManager android.content.pm  targetSdkVersion "android.content.pm.ApplicationInfo  versionCode android.content.pm.PackageInfo  versionName android.content.pm.PackageInfo  PERMISSION_GRANTED !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  BLACK android.graphics.Color  GREEN android.graphics.Color  RED android.graphics.Color  WHITE android.graphics.Color  AudioAttributes 
android.media  AudioManager 
android.media  MediaPlayer 
android.media  RingtoneManager 
android.media  Builder android.media.AudioAttributes  CONTENT_TYPE_SONIFICATION android.media.AudioAttributes  USAGE_NOTIFICATION_RINGTONE android.media.AudioAttributes  build %android.media.AudioAttributes.Builder  setContentType %android.media.AudioAttributes.Builder  setUsage %android.media.AudioAttributes.Builder  AudioAttributes android.media.MediaPlayer  OnErrorListener android.media.MediaPlayer  OnPreparedListener android.media.MediaPlayer  RingtoneManager android.media.MediaPlayer  -VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING android.media.MediaPlayer  apply android.media.MediaPlayer  	isLooping android.media.MediaPlayer  	isPlaying android.media.MediaPlayer  let android.media.MediaPlayer  prepare android.media.MediaPlayer  release android.media.MediaPlayer  setAudioAttributes android.media.MediaPlayer  
setDataSource android.media.MediaPlayer  setVideoScalingMode android.media.MediaPlayer  start android.media.MediaPlayer  stop android.media.MediaPlayer  <SAM-CONSTRUCTOR> )android.media.MediaPlayer.OnErrorListener  <SAM-CONSTRUCTOR> ,android.media.MediaPlayer.OnPreparedListener  
TYPE_RINGTONE android.media.RingtoneManager  
getDefaultUri android.media.RingtoneManager  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  IBinder 
android.os  PowerManager 
android.os  Vibrator 
android.os  RELEASE android.os.Build.VERSION  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  O_MR1 android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  ACQUIRE_CAUSES_WAKEUP android.os.PowerManager  FULL_WAKE_LOCK android.os.PowerManager  ON_AFTER_RELEASE android.os.PowerManager  PARTIAL_WAKE_LOCK android.os.PowerManager  WakeLock android.os.PowerManager  newWakeLock android.os.PowerManager  acquire  android.os.PowerManager.WakeLock  isHeld  android.os.PowerManager.WakeLock  let  android.os.PowerManager.WakeLock  release  android.os.PowerManager.WakeLock  createWaveform android.os.VibrationEffect  cancel android.os.Vibrator  let android.os.Vibrator  vibrate android.os.Vibrator  Settings android.provider  #ACTION_APPLICATION_DETAILS_SETTINGS android.provider.Settings   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  canDrawOverlays android.provider.Settings  TelecomManager android.telecom  ACTION_CHANGE_DEFAULT_DIALER android.telecom.TelecomManager  (EXTRA_CHANGE_DEFAULT_DIALER_PACKAGE_NAME android.telecom.TelecomManager  acceptRingingCall android.telecom.TelecomManager  defaultDialerPackage android.telecom.TelecomManager  endCall android.telecom.TelecomManager  TelephonyManager android.telephony  EXTRA_INCOMING_NUMBER "android.telephony.TelephonyManager  EXTRA_STATE "android.telephony.TelephonyManager  EXTRA_STATE_IDLE "android.telephony.TelephonyManager  EXTRA_STATE_OFFHOOK "android.telephony.TelephonyManager  EXTRA_STATE_RINGING "android.telephony.TelephonyManager  Log android.util  d android.util.Log  e android.util.Log  View android.view  
WindowManager android.view  ACTION_FINISH_ACTIVITY  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  MediaPlayer  android.view.ContextThemeWrapper  PowerManager  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  TelecomManager  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  	VideoView  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  
answerCall  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  declineCall  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  
isInitialized  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  phoneNumber  android.view.ContextThemeWrapper  GONE android.view.View  OnClickListener android.view.View  SYSTEM_UI_FLAG_FULLSCREEN android.view.View  SYSTEM_UI_FLAG_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_IMMERSIVE_STICKY android.view.View  TEXT_ALIGNMENT_CENTER android.view.View  layoutParams android.view.View  setBackgroundColor android.view.View  setOnClickListener android.view.View  
setPadding android.view.View  systemUiVisibility android.view.View  
textAlignment android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  addView android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  
setMargins )android.view.ViewGroup.MarginLayoutParams  addFlags android.view.Window  	decorView android.view.Window  FLAG_DISMISS_KEYGUARD 'android.view.WindowManager.LayoutParams  FLAG_FULLSCREEN 'android.view.WindowManager.LayoutParams  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  FLAG_SHOW_WHEN_LOCKED 'android.view.WindowManager.LayoutParams  FLAG_TURN_SCREEN_ON 'android.view.WindowManager.LayoutParams  Button android.widget  LinearLayout android.widget  TextView android.widget  	VideoView android.widget  LinearLayout android.widget.Button  android android.widget.Button  
answerCall android.widget.Button  apply android.widget.Button  declineCall android.widget.Button  layoutParams android.widget.Button  setBackgroundColor android.widget.Button  setOnClickListener android.widget.Button  setTextColor android.widget.Button  text android.widget.Button  textSize android.widget.Button  
HORIZONTAL android.widget.LinearLayout  LayoutParams android.widget.LinearLayout  LinearLayout android.widget.LinearLayout  VERTICAL android.widget.LinearLayout  addView android.widget.LinearLayout  android android.widget.LinearLayout  apply android.widget.LinearLayout  orientation android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  
setPadding android.widget.LinearLayout  MATCH_PARENT (android.widget.LinearLayout.LayoutParams  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  apply (android.widget.LinearLayout.LayoutParams  
setMargins (android.widget.LinearLayout.LayoutParams  View android.widget.TextView  android android.widget.TextView  apply android.widget.TextView  phoneNumber android.widget.TextView  
setPadding android.widget.TextView  setTextColor android.widget.TextView  text android.widget.TextView  
textAlignment android.widget.TextView  textSize android.widget.TextView  LinearLayout android.widget.VideoView  apply android.widget.VideoView  layoutParams android.widget.VideoView  setOnErrorListener android.widget.VideoView  setOnPreparedListener android.widget.VideoView  setVideoURI android.widget.VideoView  start android.widget.VideoView  stopPlayback android.widget.VideoView  
visibility android.widget.VideoView  ComponentActivity androidx.activity  ACTION_FINISH_ACTIVITY #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  MediaPlayer #androidx.activity.ComponentActivity  PowerManager #androidx.activity.ComponentActivity  ReactActivityDelegate #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  TelecomManager #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  	VideoView #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  
answerCall #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  declineCall #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  
isInitialized #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  onNewIntent #androidx.activity.ComponentActivity  phoneNumber #androidx.activity.ComponentActivity  ACTION_FINISH_ACTIVITY -androidx.activity.ComponentActivity.Companion  Build -androidx.activity.ComponentActivity.Companion  Button -androidx.activity.ComponentActivity.Companion  Context -androidx.activity.ComponentActivity.Companion  DefaultReactActivityDelegate -androidx.activity.ComponentActivity.Companion  LinearLayout -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  MediaPlayer -androidx.activity.ComponentActivity.Companion  PowerManager -androidx.activity.ComponentActivity.Companion  TAG -androidx.activity.ComponentActivity.Companion  TextView -androidx.activity.ComponentActivity.Companion  Uri -androidx.activity.ComponentActivity.Companion  	VideoView -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  
WindowManager -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  
answerCall -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  declineCall -androidx.activity.ComponentActivity.Companion  
fabricEnabled -androidx.activity.ComponentActivity.Companion  
isInitialized -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  phoneNumber -androidx.activity.ComponentActivity.Companion  WakeLock 0androidx.activity.ComponentActivity.PowerManager  AppCompatActivity androidx.appcompat.app  ACTION_FINISH_ACTIVITY (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  Button (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  LinearLayout (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  MediaPlayer (androidx.appcompat.app.AppCompatActivity  PowerManager (androidx.appcompat.app.AppCompatActivity  Suppress (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  TelecomManager (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  	VideoView (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  
WindowManager (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  
answerCall (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  declineCall (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  
isInitialized (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onNewIntent (androidx.appcompat.app.AppCompatActivity  phoneNumber (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  NotificationCompat androidx.core.app  ACTION_FINISH_ACTIVITY #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  MediaPlayer #androidx.core.app.ComponentActivity  PowerManager #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  TelecomManager #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  	VideoView #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  
answerCall #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  declineCall #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  
isInitialized #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  phoneNumber #androidx.core.app.ComponentActivity  WakeLock 0androidx.core.app.ComponentActivity.PowerManager  Builder $androidx.core.app.NotificationCompat  
CATEGORY_CALL $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setCategory ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ACTION_FINISH_ACTIVITY &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  Button &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  LinearLayout &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  MediaPlayer &androidx.fragment.app.FragmentActivity  PowerManager &androidx.fragment.app.FragmentActivity  Suppress &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  TelecomManager &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  	VideoView &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  
WindowManager &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  
answerCall &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  declineCall &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  
isInitialized &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  phoneNumber &androidx.fragment.app.FragmentActivity  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  packages com.facebook.react.PackageList  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  BuildConfig "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  VideoRingtonePackage "com.facebook.react.ReactNativeHost  apply "com.facebook.react.ReactNativeHost  NativeModule com.facebook.react.bridge  Promise com.facebook.react.bridge  ReactApplicationContext com.facebook.react.bridge  ReactContextBaseJavaModule com.facebook.react.bridge  ReactMethod com.facebook.react.bridge  WritableMap com.facebook.react.bridge  WritableNativeMap com.facebook.react.bridge  Build (com.facebook.react.bridge.BaseJavaModule  Context (com.facebook.react.bridge.BaseJavaModule  
ContextCompat (com.facebook.react.bridge.BaseJavaModule  	Exception (com.facebook.react.bridge.BaseJavaModule  Intent (com.facebook.react.bridge.BaseJavaModule  Log (com.facebook.react.bridge.BaseJavaModule  Manifest (com.facebook.react.bridge.BaseJavaModule  PackageManager (com.facebook.react.bridge.BaseJavaModule  RoleManager (com.facebook.react.bridge.BaseJavaModule  Settings (com.facebook.react.bridge.BaseJavaModule  TAG (com.facebook.react.bridge.BaseJavaModule  TelecomManager (com.facebook.react.bridge.BaseJavaModule  Uri (com.facebook.react.bridge.BaseJavaModule  VideoRingtoneActivity (com.facebook.react.bridge.BaseJavaModule  VideoRingtoneService (com.facebook.react.bridge.BaseJavaModule  WritableNativeMap (com.facebook.react.bridge.BaseJavaModule  apply (com.facebook.react.bridge.BaseJavaModule  java (com.facebook.react.bridge.BaseJavaModule  reactApplicationContext (com.facebook.react.bridge.BaseJavaModule  reject !com.facebook.react.bridge.Promise  resolve !com.facebook.react.bridge.Promise  getSystemService &com.facebook.react.bridge.ReactContext  Build 4com.facebook.react.bridge.ReactContextBaseJavaModule  Context 4com.facebook.react.bridge.ReactContextBaseJavaModule  
ContextCompat 4com.facebook.react.bridge.ReactContextBaseJavaModule  	Exception 4com.facebook.react.bridge.ReactContextBaseJavaModule  Intent 4com.facebook.react.bridge.ReactContextBaseJavaModule  Log 4com.facebook.react.bridge.ReactContextBaseJavaModule  Manifest 4com.facebook.react.bridge.ReactContextBaseJavaModule  PackageManager 4com.facebook.react.bridge.ReactContextBaseJavaModule  RoleManager 4com.facebook.react.bridge.ReactContextBaseJavaModule  Settings 4com.facebook.react.bridge.ReactContextBaseJavaModule  TAG 4com.facebook.react.bridge.ReactContextBaseJavaModule  TelecomManager 4com.facebook.react.bridge.ReactContextBaseJavaModule  Uri 4com.facebook.react.bridge.ReactContextBaseJavaModule  VideoRingtoneActivity 4com.facebook.react.bridge.ReactContextBaseJavaModule  VideoRingtoneService 4com.facebook.react.bridge.ReactContextBaseJavaModule  WritableNativeMap 4com.facebook.react.bridge.ReactContextBaseJavaModule  apply 4com.facebook.react.bridge.ReactContextBaseJavaModule  java 4com.facebook.react.bridge.ReactContextBaseJavaModule  reactApplicationContext 4com.facebook.react.bridge.ReactContextBaseJavaModule  
putBoolean +com.facebook.react.bridge.WritableNativeMap  putInt +com.facebook.react.bridge.WritableNativeMap  	putString +com.facebook.react.bridge.WritableNativeMap  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  load <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  getDefaultReactHost ,com.facebook.react.defaults.DefaultReactHost  OpenSourceMergedSoMapping com.facebook.react.soloader  ViewManager com.facebook.react.uimanager  SoLoader com.facebook.soloader  init com.facebook.soloader.SoLoader  ACTION_FINISH_ACTIVITY com.videoringtoneapp  ACTION_START_VIDEO_RINGTONE com.videoringtoneapp  ACTION_STOP_VIDEO_RINGTONE com.videoringtoneapp  AppCompatActivity com.videoringtoneapp  Application com.videoringtoneapp  AudioAttributes com.videoringtoneapp  Boolean com.videoringtoneapp  BroadcastReceiver com.videoringtoneapp  Build com.videoringtoneapp  BuildConfig com.videoringtoneapp  Bundle com.videoringtoneapp  Button com.videoringtoneapp  
CHANNEL_ID com.videoringtoneapp  CallStateReceiver com.videoringtoneapp  Context com.videoringtoneapp  
ContextCompat com.videoringtoneapp  DefaultReactActivityDelegate com.videoringtoneapp  DefaultReactNativeHost com.videoringtoneapp  	Exception com.videoringtoneapp  IBinder com.videoringtoneapp  Int com.videoringtoneapp  Intent com.videoringtoneapp  LinearLayout com.videoringtoneapp  List com.videoringtoneapp  Log com.videoringtoneapp  MainActivity com.videoringtoneapp  MainApplication com.videoringtoneapp  Manifest com.videoringtoneapp  MediaPlayer com.videoringtoneapp  NOTIFICATION_ID com.videoringtoneapp  NativeModule com.videoringtoneapp  Notification com.videoringtoneapp  NotificationChannel com.videoringtoneapp  NotificationCompat com.videoringtoneapp  NotificationManager com.videoringtoneapp  OpenSourceMergedSoMapping com.videoringtoneapp  PackageList com.videoringtoneapp  PackageManager com.videoringtoneapp  
PendingIntent com.videoringtoneapp  PowerManager com.videoringtoneapp  Promise com.videoringtoneapp  
ReactActivity com.videoringtoneapp  ReactActivityDelegate com.videoringtoneapp  ReactApplication com.videoringtoneapp  ReactApplicationContext com.videoringtoneapp  ReactContextBaseJavaModule com.videoringtoneapp  	ReactHost com.videoringtoneapp  ReactMethod com.videoringtoneapp  ReactNativeHost com.videoringtoneapp  ReactPackage com.videoringtoneapp  RingtoneManager com.videoringtoneapp  RoleManager com.videoringtoneapp  START_NOT_STICKY com.videoringtoneapp  Service com.videoringtoneapp  Settings com.videoringtoneapp  SoLoader com.videoringtoneapp  String com.videoringtoneapp  Suppress com.videoringtoneapp  TAG com.videoringtoneapp  TelecomManager com.videoringtoneapp  TelephonyManager com.videoringtoneapp  TextView com.videoringtoneapp  Uri com.videoringtoneapp  Vibrator com.videoringtoneapp  VideoRingtoneActivity com.videoringtoneapp  VideoRingtoneModule com.videoringtoneapp  VideoRingtonePackage com.videoringtoneapp  VideoRingtoneService com.videoringtoneapp  	VideoView com.videoringtoneapp  View com.videoringtoneapp  ViewManager com.videoringtoneapp  
WindowManager com.videoringtoneapp  WritableNativeMap com.videoringtoneapp  android com.videoringtoneapp  
answerCall com.videoringtoneapp  apply com.videoringtoneapp  declineCall com.videoringtoneapp  	emptyList com.videoringtoneapp  
fabricEnabled com.videoringtoneapp  getDefaultReactHost com.videoringtoneapp  
isInitialized com.videoringtoneapp  java com.videoringtoneapp  let com.videoringtoneapp  listOf com.videoringtoneapp  load com.videoringtoneapp  longArrayOf com.videoringtoneapp  phoneNumber com.videoringtoneapp  reactApplicationContext com.videoringtoneapp  DEBUG  com.videoringtoneapp.BuildConfig  IS_HERMES_ENABLED  com.videoringtoneapp.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED  com.videoringtoneapp.BuildConfig  Context &com.videoringtoneapp.CallStateReceiver  	Exception &com.videoringtoneapp.CallStateReceiver  Intent &com.videoringtoneapp.CallStateReceiver  Log &com.videoringtoneapp.CallStateReceiver  String &com.videoringtoneapp.CallStateReceiver  TAG &com.videoringtoneapp.CallStateReceiver  TelephonyManager &com.videoringtoneapp.CallStateReceiver  VideoRingtoneActivity &com.videoringtoneapp.CallStateReceiver  VideoRingtoneService &com.videoringtoneapp.CallStateReceiver  apply &com.videoringtoneapp.CallStateReceiver  handleCallAnswered &com.videoringtoneapp.CallStateReceiver  handleCallEnded &com.videoringtoneapp.CallStateReceiver  handleIncomingCall &com.videoringtoneapp.CallStateReceiver  java &com.videoringtoneapp.CallStateReceiver  Intent 0com.videoringtoneapp.CallStateReceiver.Companion  Log 0com.videoringtoneapp.CallStateReceiver.Companion  TAG 0com.videoringtoneapp.CallStateReceiver.Companion  TelephonyManager 0com.videoringtoneapp.CallStateReceiver.Companion  VideoRingtoneActivity 0com.videoringtoneapp.CallStateReceiver.Companion  VideoRingtoneService 0com.videoringtoneapp.CallStateReceiver.Companion  apply 0com.videoringtoneapp.CallStateReceiver.Companion  java 0com.videoringtoneapp.CallStateReceiver.Companion  DefaultReactActivityDelegate !com.videoringtoneapp.MainActivity  
fabricEnabled !com.videoringtoneapp.MainActivity  mainComponentName !com.videoringtoneapp.MainActivity  BuildConfig $com.videoringtoneapp.MainApplication  OpenSourceMergedSoMapping $com.videoringtoneapp.MainApplication  PackageList $com.videoringtoneapp.MainApplication  SoLoader $com.videoringtoneapp.MainApplication  VideoRingtonePackage $com.videoringtoneapp.MainApplication  applicationContext $com.videoringtoneapp.MainApplication  apply $com.videoringtoneapp.MainApplication  getDefaultReactHost $com.videoringtoneapp.MainApplication  load $com.videoringtoneapp.MainApplication  reactNativeHost $com.videoringtoneapp.MainApplication  WakeLock !com.videoringtoneapp.PowerManager  ACTION_FINISH_ACTIVITY *com.videoringtoneapp.VideoRingtoneActivity  Build *com.videoringtoneapp.VideoRingtoneActivity  Bundle *com.videoringtoneapp.VideoRingtoneActivity  Button *com.videoringtoneapp.VideoRingtoneActivity  	Companion *com.videoringtoneapp.VideoRingtoneActivity  Context *com.videoringtoneapp.VideoRingtoneActivity  	Exception *com.videoringtoneapp.VideoRingtoneActivity  Intent *com.videoringtoneapp.VideoRingtoneActivity  LinearLayout *com.videoringtoneapp.VideoRingtoneActivity  Log *com.videoringtoneapp.VideoRingtoneActivity  MediaPlayer *com.videoringtoneapp.VideoRingtoneActivity  PowerManager *com.videoringtoneapp.VideoRingtoneActivity  String *com.videoringtoneapp.VideoRingtoneActivity  Suppress *com.videoringtoneapp.VideoRingtoneActivity  TAG *com.videoringtoneapp.VideoRingtoneActivity  TelecomManager *com.videoringtoneapp.VideoRingtoneActivity  TextView *com.videoringtoneapp.VideoRingtoneActivity  Uri *com.videoringtoneapp.VideoRingtoneActivity  	VideoView *com.videoringtoneapp.VideoRingtoneActivity  View *com.videoringtoneapp.VideoRingtoneActivity  
WindowManager *com.videoringtoneapp.VideoRingtoneActivity  android *com.videoringtoneapp.VideoRingtoneActivity  answerButton *com.videoringtoneapp.VideoRingtoneActivity  
answerCall *com.videoringtoneapp.VideoRingtoneActivity  apply *com.videoringtoneapp.VideoRingtoneActivity  buttonContainer *com.videoringtoneapp.VideoRingtoneActivity  callerNameText *com.videoringtoneapp.VideoRingtoneActivity  createUI *com.videoringtoneapp.VideoRingtoneActivity  
declineButton *com.videoringtoneapp.VideoRingtoneActivity  declineCall *com.videoringtoneapp.VideoRingtoneActivity  finish *com.videoringtoneapp.VideoRingtoneActivity  finishActivity *com.videoringtoneapp.VideoRingtoneActivity  getSystemService *com.videoringtoneapp.VideoRingtoneActivity  getVideoUri *com.videoringtoneapp.VideoRingtoneActivity  intent *com.videoringtoneapp.VideoRingtoneActivity  
isInitialized *com.videoringtoneapp.VideoRingtoneActivity  let *com.videoringtoneapp.VideoRingtoneActivity  packageName *com.videoringtoneapp.VideoRingtoneActivity  phoneNumber *com.videoringtoneapp.VideoRingtoneActivity  setContentView *com.videoringtoneapp.VideoRingtoneActivity  setShowWhenLocked *com.videoringtoneapp.VideoRingtoneActivity  setTurnScreenOn *com.videoringtoneapp.VideoRingtoneActivity  setupVideoPlayer *com.videoringtoneapp.VideoRingtoneActivity  
setupWakeLock *com.videoringtoneapp.VideoRingtoneActivity  setupWindow *com.videoringtoneapp.VideoRingtoneActivity  	videoView *com.videoringtoneapp.VideoRingtoneActivity  wakeLock *com.videoringtoneapp.VideoRingtoneActivity  window *com.videoringtoneapp.VideoRingtoneActivity  ACTION_FINISH_ACTIVITY 4com.videoringtoneapp.VideoRingtoneActivity.Companion  Build 4com.videoringtoneapp.VideoRingtoneActivity.Companion  Button 4com.videoringtoneapp.VideoRingtoneActivity.Companion  Context 4com.videoringtoneapp.VideoRingtoneActivity.Companion  LinearLayout 4com.videoringtoneapp.VideoRingtoneActivity.Companion  Log 4com.videoringtoneapp.VideoRingtoneActivity.Companion  MediaPlayer 4com.videoringtoneapp.VideoRingtoneActivity.Companion  PowerManager 4com.videoringtoneapp.VideoRingtoneActivity.Companion  TAG 4com.videoringtoneapp.VideoRingtoneActivity.Companion  TextView 4com.videoringtoneapp.VideoRingtoneActivity.Companion  Uri 4com.videoringtoneapp.VideoRingtoneActivity.Companion  	VideoView 4com.videoringtoneapp.VideoRingtoneActivity.Companion  View 4com.videoringtoneapp.VideoRingtoneActivity.Companion  
WindowManager 4com.videoringtoneapp.VideoRingtoneActivity.Companion  android 4com.videoringtoneapp.VideoRingtoneActivity.Companion  
answerCall 4com.videoringtoneapp.VideoRingtoneActivity.Companion  apply 4com.videoringtoneapp.VideoRingtoneActivity.Companion  declineCall 4com.videoringtoneapp.VideoRingtoneActivity.Companion  
isInitialized 4com.videoringtoneapp.VideoRingtoneActivity.Companion  let 4com.videoringtoneapp.VideoRingtoneActivity.Companion  phoneNumber 4com.videoringtoneapp.VideoRingtoneActivity.Companion  WakeLock 7com.videoringtoneapp.VideoRingtoneActivity.PowerManager  Build (com.videoringtoneapp.VideoRingtoneModule  Context (com.videoringtoneapp.VideoRingtoneModule  
ContextCompat (com.videoringtoneapp.VideoRingtoneModule  	Exception (com.videoringtoneapp.VideoRingtoneModule  Intent (com.videoringtoneapp.VideoRingtoneModule  Log (com.videoringtoneapp.VideoRingtoneModule  Manifest (com.videoringtoneapp.VideoRingtoneModule  PackageManager (com.videoringtoneapp.VideoRingtoneModule  Promise (com.videoringtoneapp.VideoRingtoneModule  ReactApplicationContext (com.videoringtoneapp.VideoRingtoneModule  ReactMethod (com.videoringtoneapp.VideoRingtoneModule  RoleManager (com.videoringtoneapp.VideoRingtoneModule  Settings (com.videoringtoneapp.VideoRingtoneModule  String (com.videoringtoneapp.VideoRingtoneModule  TAG (com.videoringtoneapp.VideoRingtoneModule  TelecomManager (com.videoringtoneapp.VideoRingtoneModule  Uri (com.videoringtoneapp.VideoRingtoneModule  VideoRingtoneActivity (com.videoringtoneapp.VideoRingtoneModule  VideoRingtoneService (com.videoringtoneapp.VideoRingtoneModule  WritableNativeMap (com.videoringtoneapp.VideoRingtoneModule  apply (com.videoringtoneapp.VideoRingtoneModule  java (com.videoringtoneapp.VideoRingtoneModule  reactApplicationContext (com.videoringtoneapp.VideoRingtoneModule  Build 2com.videoringtoneapp.VideoRingtoneModule.Companion  Context 2com.videoringtoneapp.VideoRingtoneModule.Companion  
ContextCompat 2com.videoringtoneapp.VideoRingtoneModule.Companion  Intent 2com.videoringtoneapp.VideoRingtoneModule.Companion  Log 2com.videoringtoneapp.VideoRingtoneModule.Companion  Manifest 2com.videoringtoneapp.VideoRingtoneModule.Companion  PackageManager 2com.videoringtoneapp.VideoRingtoneModule.Companion  RoleManager 2com.videoringtoneapp.VideoRingtoneModule.Companion  Settings 2com.videoringtoneapp.VideoRingtoneModule.Companion  TAG 2com.videoringtoneapp.VideoRingtoneModule.Companion  TelecomManager 2com.videoringtoneapp.VideoRingtoneModule.Companion  Uri 2com.videoringtoneapp.VideoRingtoneModule.Companion  VideoRingtoneActivity 2com.videoringtoneapp.VideoRingtoneModule.Companion  VideoRingtoneService 2com.videoringtoneapp.VideoRingtoneModule.Companion  WritableNativeMap 2com.videoringtoneapp.VideoRingtoneModule.Companion  apply 2com.videoringtoneapp.VideoRingtoneModule.Companion  java 2com.videoringtoneapp.VideoRingtoneModule.Companion  reactApplicationContext 2com.videoringtoneapp.VideoRingtoneModule.Companion  VideoRingtoneModule )com.videoringtoneapp.VideoRingtonePackage  	emptyList )com.videoringtoneapp.VideoRingtonePackage  listOf )com.videoringtoneapp.VideoRingtonePackage  ACTION_START_VIDEO_RINGTONE )com.videoringtoneapp.VideoRingtoneService  ACTION_STOP_VIDEO_RINGTONE )com.videoringtoneapp.VideoRingtoneService  AudioAttributes )com.videoringtoneapp.VideoRingtoneService  Build )com.videoringtoneapp.VideoRingtoneService  
CHANNEL_ID )com.videoringtoneapp.VideoRingtoneService  	Companion )com.videoringtoneapp.VideoRingtoneService  Context )com.videoringtoneapp.VideoRingtoneService  	Exception )com.videoringtoneapp.VideoRingtoneService  IBinder )com.videoringtoneapp.VideoRingtoneService  Int )com.videoringtoneapp.VideoRingtoneService  Intent )com.videoringtoneapp.VideoRingtoneService  Log )com.videoringtoneapp.VideoRingtoneService  MainActivity )com.videoringtoneapp.VideoRingtoneService  MediaPlayer )com.videoringtoneapp.VideoRingtoneService  NOTIFICATION_ID )com.videoringtoneapp.VideoRingtoneService  Notification )com.videoringtoneapp.VideoRingtoneService  NotificationChannel )com.videoringtoneapp.VideoRingtoneService  NotificationCompat )com.videoringtoneapp.VideoRingtoneService  NotificationManager )com.videoringtoneapp.VideoRingtoneService  
PendingIntent )com.videoringtoneapp.VideoRingtoneService  PowerManager )com.videoringtoneapp.VideoRingtoneService  RingtoneManager )com.videoringtoneapp.VideoRingtoneService  START_NOT_STICKY )com.videoringtoneapp.VideoRingtoneService  String )com.videoringtoneapp.VideoRingtoneService  Suppress )com.videoringtoneapp.VideoRingtoneService  TAG )com.videoringtoneapp.VideoRingtoneService  Vibrator )com.videoringtoneapp.VideoRingtoneService  android )com.videoringtoneapp.VideoRingtoneService  apply )com.videoringtoneapp.VideoRingtoneService  createNotification )com.videoringtoneapp.VideoRingtoneService  createNotificationChannel )com.videoringtoneapp.VideoRingtoneService  getSystemService )com.videoringtoneapp.VideoRingtoneService  	isRinging )com.videoringtoneapp.VideoRingtoneService  java )com.videoringtoneapp.VideoRingtoneService  let )com.videoringtoneapp.VideoRingtoneService  longArrayOf )com.videoringtoneapp.VideoRingtoneService  mediaPlayer )com.videoringtoneapp.VideoRingtoneService  startAudioRingtone )com.videoringtoneapp.VideoRingtoneService  startForeground )com.videoringtoneapp.VideoRingtoneService  startVibration )com.videoringtoneapp.VideoRingtoneService  startVideoRingtone )com.videoringtoneapp.VideoRingtoneService  stopAudioRingtone )com.videoringtoneapp.VideoRingtoneService  stopForeground )com.videoringtoneapp.VideoRingtoneService  stopSelf )com.videoringtoneapp.VideoRingtoneService  
stopVibration )com.videoringtoneapp.VideoRingtoneService  stopVideoRingtone )com.videoringtoneapp.VideoRingtoneService  vibrator )com.videoringtoneapp.VideoRingtoneService  wakeLock )com.videoringtoneapp.VideoRingtoneService  ACTION_START_VIDEO_RINGTONE 3com.videoringtoneapp.VideoRingtoneService.Companion  ACTION_STOP_VIDEO_RINGTONE 3com.videoringtoneapp.VideoRingtoneService.Companion  AudioAttributes 3com.videoringtoneapp.VideoRingtoneService.Companion  Build 3com.videoringtoneapp.VideoRingtoneService.Companion  
CHANNEL_ID 3com.videoringtoneapp.VideoRingtoneService.Companion  Context 3com.videoringtoneapp.VideoRingtoneService.Companion  Intent 3com.videoringtoneapp.VideoRingtoneService.Companion  Log 3com.videoringtoneapp.VideoRingtoneService.Companion  MainActivity 3com.videoringtoneapp.VideoRingtoneService.Companion  MediaPlayer 3com.videoringtoneapp.VideoRingtoneService.Companion  NOTIFICATION_ID 3com.videoringtoneapp.VideoRingtoneService.Companion  NotificationChannel 3com.videoringtoneapp.VideoRingtoneService.Companion  NotificationCompat 3com.videoringtoneapp.VideoRingtoneService.Companion  NotificationManager 3com.videoringtoneapp.VideoRingtoneService.Companion  
PendingIntent 3com.videoringtoneapp.VideoRingtoneService.Companion  PowerManager 3com.videoringtoneapp.VideoRingtoneService.Companion  RingtoneManager 3com.videoringtoneapp.VideoRingtoneService.Companion  START_NOT_STICKY 3com.videoringtoneapp.VideoRingtoneService.Companion  TAG 3com.videoringtoneapp.VideoRingtoneService.Companion  android 3com.videoringtoneapp.VideoRingtoneService.Companion  apply 3com.videoringtoneapp.VideoRingtoneService.Companion  java 3com.videoringtoneapp.VideoRingtoneService.Companion  let 3com.videoringtoneapp.VideoRingtoneService.Companion  longArrayOf 3com.videoringtoneapp.VideoRingtoneService.Companion  WakeLock 6com.videoringtoneapp.VideoRingtoneService.PowerManager  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  add java.util.ArrayList  	Function1 kotlin  	Function3 kotlin  	LongArray kotlin  Suppress kotlin  apply kotlin  
isInitialized kotlin  let kotlin  longArrayOf kotlin  not kotlin.Boolean  	compareTo 
kotlin.Int  or 
kotlin.Int  message kotlin.Throwable  List kotlin.collections  	emptyList kotlin.collections  listOf kotlin.collections  java 
kotlin.jvm  KMutableProperty0 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Activity android.app  File android.app.Activity  	RESULT_OK android.app.Activity  VIDEO_RINGTONE_FILENAME android.app.Activity  endsWith android.app.Activity  filter android.app.Activity  
isNullOrEmpty android.app.Activity  startActivityForResult android.app.Activity  openInputStream android.content.ContentResolver  query android.content.ContentResolver  File android.content.Context  MODE_PRIVATE android.content.Context  VIDEO_RINGTONE_FILENAME android.content.Context  endsWith android.content.Context  filter android.content.Context  
isNullOrEmpty android.content.Context  File android.content.ContextWrapper  VIDEO_RINGTONE_FILENAME android.content.ContextWrapper  contentResolver android.content.ContextWrapper  endsWith android.content.ContextWrapper  filesDir android.content.ContextWrapper  filter android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  ACTION_GET_CONTENT android.content.Intent  CATEGORY_OPENABLE android.content.Intent  EXTRA_MIME_TYPES android.content.Intent  addCategory android.content.Intent  arrayOf android.content.Intent  
createChooser android.content.Intent  type android.content.Intent  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  list  android.content.res.AssetManager  Cursor android.database  getColumnIndex android.database.Cursor  getLong android.database.Cursor  	getString android.database.Cursor  moveToFirst android.database.Cursor  use android.database.Cursor  fromFile android.net.Uri  toString android.net.Uri  
MediaStore android.provider  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  SIZE (android.provider.MediaStore.MediaColumns  w android.util.Log  File  android.view.ContextThemeWrapper  VIDEO_RINGTONE_FILENAME  android.view.ContextThemeWrapper  assets  android.view.ContextThemeWrapper  endsWith  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  
isNullOrEmpty  android.view.ContextThemeWrapper  File #androidx.activity.ComponentActivity  VIDEO_RINGTONE_FILENAME #androidx.activity.ComponentActivity  endsWith #androidx.activity.ComponentActivity  filter #androidx.activity.ComponentActivity  
isNullOrEmpty #androidx.activity.ComponentActivity  File -androidx.activity.ComponentActivity.Companion  VIDEO_RINGTONE_FILENAME -androidx.activity.ComponentActivity.Companion  endsWith -androidx.activity.ComponentActivity.Companion  filter -androidx.activity.ComponentActivity.Companion  
isNullOrEmpty -androidx.activity.ComponentActivity.Companion  File (androidx.appcompat.app.AppCompatActivity  VIDEO_RINGTONE_FILENAME (androidx.appcompat.app.AppCompatActivity  endsWith (androidx.appcompat.app.AppCompatActivity  filter (androidx.appcompat.app.AppCompatActivity  
isNullOrEmpty (androidx.appcompat.app.AppCompatActivity  File #androidx.core.app.ComponentActivity  VIDEO_RINGTONE_FILENAME #androidx.core.app.ComponentActivity  endsWith #androidx.core.app.ComponentActivity  filter #androidx.core.app.ComponentActivity  
isNullOrEmpty #androidx.core.app.ComponentActivity  File &androidx.fragment.app.FragmentActivity  VIDEO_RINGTONE_FILENAME &androidx.fragment.app.FragmentActivity  endsWith &androidx.fragment.app.FragmentActivity  filter &androidx.fragment.app.FragmentActivity  
isNullOrEmpty &androidx.fragment.app.FragmentActivity  ActivityEventListener com.facebook.react.bridge  BaseActivityEventListener com.facebook.react.bridge  PICK_VIDEO_REQUEST 3com.facebook.react.bridge.BaseActivityEventListener  handleVideoPickerResult 3com.facebook.react.bridge.BaseActivityEventListener  Activity (com.facebook.react.bridge.BaseJavaModule  BaseActivityEventListener (com.facebook.react.bridge.BaseJavaModule  Cursor (com.facebook.react.bridge.BaseJavaModule  File (com.facebook.react.bridge.BaseJavaModule  FileOutputStream (com.facebook.react.bridge.BaseJavaModule  InputStream (com.facebook.react.bridge.BaseJavaModule  Int (com.facebook.react.bridge.BaseJavaModule  
MediaStore (com.facebook.react.bridge.BaseJavaModule  PICK_VIDEO_REQUEST (com.facebook.react.bridge.BaseJavaModule  
PREFS_NAME (com.facebook.react.bridge.BaseJavaModule  PREF_VIDEO_PATH (com.facebook.react.bridge.BaseJavaModule  VIDEO_RINGTONE_FILENAME (com.facebook.react.bridge.BaseJavaModule  arrayOf (com.facebook.react.bridge.BaseJavaModule  copyTo (com.facebook.react.bridge.BaseJavaModule  handleVideoPickerResult (com.facebook.react.bridge.BaseJavaModule  use (com.facebook.react.bridge.BaseJavaModule  addActivityEventListener &com.facebook.react.bridge.ReactContext  Activity 4com.facebook.react.bridge.ReactContextBaseJavaModule  BaseActivityEventListener 4com.facebook.react.bridge.ReactContextBaseJavaModule  Cursor 4com.facebook.react.bridge.ReactContextBaseJavaModule  File 4com.facebook.react.bridge.ReactContextBaseJavaModule  FileOutputStream 4com.facebook.react.bridge.ReactContextBaseJavaModule  InputStream 4com.facebook.react.bridge.ReactContextBaseJavaModule  Int 4com.facebook.react.bridge.ReactContextBaseJavaModule  
MediaStore 4com.facebook.react.bridge.ReactContextBaseJavaModule  PICK_VIDEO_REQUEST 4com.facebook.react.bridge.ReactContextBaseJavaModule  
PREFS_NAME 4com.facebook.react.bridge.ReactContextBaseJavaModule  PREF_VIDEO_PATH 4com.facebook.react.bridge.ReactContextBaseJavaModule  VIDEO_RINGTONE_FILENAME 4com.facebook.react.bridge.ReactContextBaseJavaModule  arrayOf 4com.facebook.react.bridge.ReactContextBaseJavaModule  copyTo 4com.facebook.react.bridge.ReactContextBaseJavaModule  currentActivity 4com.facebook.react.bridge.ReactContextBaseJavaModule  handleVideoPickerResult 4com.facebook.react.bridge.ReactContextBaseJavaModule  use 4com.facebook.react.bridge.ReactContextBaseJavaModule  apply +com.facebook.react.bridge.WritableNativeMap  	putDouble +com.facebook.react.bridge.WritableNativeMap  putMap +com.facebook.react.bridge.WritableNativeMap  Activity com.videoringtoneapp  BaseActivityEventListener com.videoringtoneapp  Cursor com.videoringtoneapp  File com.videoringtoneapp  FileOutputStream com.videoringtoneapp  InputStream com.videoringtoneapp  
MediaStore com.videoringtoneapp  PICK_VIDEO_REQUEST com.videoringtoneapp  
PREFS_NAME com.videoringtoneapp  PREF_VIDEO_PATH com.videoringtoneapp  VIDEO_RINGTONE_FILENAME com.videoringtoneapp  WritableMap com.videoringtoneapp  arrayOf com.videoringtoneapp  copyTo com.videoringtoneapp  endsWith com.videoringtoneapp  filter com.videoringtoneapp  handleVideoPickerResult com.videoringtoneapp  
isNullOrEmpty com.videoringtoneapp  use com.videoringtoneapp  File *com.videoringtoneapp.VideoRingtoneActivity  VIDEO_RINGTONE_FILENAME *com.videoringtoneapp.VideoRingtoneActivity  assets *com.videoringtoneapp.VideoRingtoneActivity  endsWith *com.videoringtoneapp.VideoRingtoneActivity  filesDir *com.videoringtoneapp.VideoRingtoneActivity  filter *com.videoringtoneapp.VideoRingtoneActivity  
isNullOrEmpty *com.videoringtoneapp.VideoRingtoneActivity  File 4com.videoringtoneapp.VideoRingtoneActivity.Companion  VIDEO_RINGTONE_FILENAME 4com.videoringtoneapp.VideoRingtoneActivity.Companion  endsWith 4com.videoringtoneapp.VideoRingtoneActivity.Companion  filter 4com.videoringtoneapp.VideoRingtoneActivity.Companion  
isNullOrEmpty 4com.videoringtoneapp.VideoRingtoneActivity.Companion  Activity (com.videoringtoneapp.VideoRingtoneModule  BaseActivityEventListener (com.videoringtoneapp.VideoRingtoneModule  Cursor (com.videoringtoneapp.VideoRingtoneModule  File (com.videoringtoneapp.VideoRingtoneModule  FileOutputStream (com.videoringtoneapp.VideoRingtoneModule  InputStream (com.videoringtoneapp.VideoRingtoneModule  Int (com.videoringtoneapp.VideoRingtoneModule  
MediaStore (com.videoringtoneapp.VideoRingtoneModule  PICK_VIDEO_REQUEST (com.videoringtoneapp.VideoRingtoneModule  
PREFS_NAME (com.videoringtoneapp.VideoRingtoneModule  PREF_VIDEO_PATH (com.videoringtoneapp.VideoRingtoneModule  VIDEO_RINGTONE_FILENAME (com.videoringtoneapp.VideoRingtoneModule  WritableMap (com.videoringtoneapp.VideoRingtoneModule  activityEventListener (com.videoringtoneapp.VideoRingtoneModule  arrayOf (com.videoringtoneapp.VideoRingtoneModule  copyTo (com.videoringtoneapp.VideoRingtoneModule  currentActivity (com.videoringtoneapp.VideoRingtoneModule  getFileInfo (com.videoringtoneapp.VideoRingtoneModule  handleVideoPickerResult (com.videoringtoneapp.VideoRingtoneModule  pickVideoPromise (com.videoringtoneapp.VideoRingtoneModule  use (com.videoringtoneapp.VideoRingtoneModule  Activity 2com.videoringtoneapp.VideoRingtoneModule.Companion  File 2com.videoringtoneapp.VideoRingtoneModule.Companion  FileOutputStream 2com.videoringtoneapp.VideoRingtoneModule.Companion  
MediaStore 2com.videoringtoneapp.VideoRingtoneModule.Companion  PICK_VIDEO_REQUEST 2com.videoringtoneapp.VideoRingtoneModule.Companion  
PREFS_NAME 2com.videoringtoneapp.VideoRingtoneModule.Companion  PREF_VIDEO_PATH 2com.videoringtoneapp.VideoRingtoneModule.Companion  VIDEO_RINGTONE_FILENAME 2com.videoringtoneapp.VideoRingtoneModule.Companion  arrayOf 2com.videoringtoneapp.VideoRingtoneModule.Companion  copyTo 2com.videoringtoneapp.VideoRingtoneModule.Companion  handleVideoPickerResult 2com.videoringtoneapp.VideoRingtoneModule.Companion  use 2com.videoringtoneapp.VideoRingtoneModule.Companion  File java.io  FileOutputStream java.io  InputStream java.io  absolutePath java.io.File  delete java.io.File  exists java.io.File  mkdirs java.io.File  use java.io.FileOutputStream  copyTo java.io.InputStream  use java.io.InputStream  Array kotlin  CharSequence kotlin  Nothing kotlin  arrayOf kotlin  use kotlin  toDouble kotlin.Long  Map kotlin.collections  filter kotlin.collections  
isNullOrEmpty kotlin.collections  get kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  copyTo 	kotlin.io  endsWith 	kotlin.io  use 	kotlin.io  Sequence kotlin.sequences  filter kotlin.sequences  endsWith kotlin.text  filter kotlin.text  
isNullOrEmpty kotlin.text  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_MEDIA_VIDEO android.Manifest.permission  BRAND android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  TIRAMISU android.os.Build.VERSION_CODES  contains (com.facebook.react.bridge.BaseJavaModule  	lowercase (com.facebook.react.bridge.BaseJavaModule  packageName 1com.facebook.react.bridge.ReactApplicationContext  
startActivity 1com.facebook.react.bridge.ReactApplicationContext  contains 4com.facebook.react.bridge.ReactContextBaseJavaModule  	lowercase 4com.facebook.react.bridge.ReactContextBaseJavaModule  contains com.videoringtoneapp  	lowercase com.videoringtoneapp  contains (com.videoringtoneapp.VideoRingtoneModule  fallbackToTelecomManager (com.videoringtoneapp.VideoRingtoneModule  	lowercase (com.videoringtoneapp.VideoRingtoneModule  contains 2com.videoringtoneapp.VideoRingtoneModule.Companion  	lowercase 2com.videoringtoneapp.VideoRingtoneModule.Companion  contains 
kotlin.String  contains kotlin.collections  contains 
kotlin.ranges  contains kotlin.sequences  contains kotlin.text  	lowercase kotlin.text  Intent android.app.Activity  MainActivity android.app.Activity  Toast android.app.Activity  java android.app.Activity  
startActivity android.app.Activity  Toast android.content.Context  Toast android.content.ContextWrapper  ACTION_CALL android.content.Intent  ACTION_DIAL android.content.Intent  ACTION_VIEW android.content.Intent  Settings android.content.Intent  schemeSpecificPart android.net.Uri  #ACTION_MANAGE_DEFAULT_APPS_SETTINGS android.provider.Settings  ACTION_SETTINGS android.provider.Settings  Intent  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  listOf (com.facebook.react.bridge.BaseJavaModule  	withIndex (com.facebook.react.bridge.BaseJavaModule  listOf 4com.facebook.react.bridge.ReactContextBaseJavaModule  	withIndex 4com.facebook.react.bridge.ReactContextBaseJavaModule  DialerActivity com.videoringtoneapp  Toast com.videoringtoneapp  	withIndex com.videoringtoneapp  Bundle #com.videoringtoneapp.DialerActivity  	Exception #com.videoringtoneapp.DialerActivity  Intent #com.videoringtoneapp.DialerActivity  Log #com.videoringtoneapp.DialerActivity  MainActivity #com.videoringtoneapp.DialerActivity  TAG #com.videoringtoneapp.DialerActivity  Toast #com.videoringtoneapp.DialerActivity  Uri #com.videoringtoneapp.DialerActivity  apply #com.videoringtoneapp.DialerActivity  finish #com.videoringtoneapp.DialerActivity  handleIntent #com.videoringtoneapp.DialerActivity  intent #com.videoringtoneapp.DialerActivity  java #com.videoringtoneapp.DialerActivity  openMainApp #com.videoringtoneapp.DialerActivity  
startActivity #com.videoringtoneapp.DialerActivity  Intent -com.videoringtoneapp.DialerActivity.Companion  Log -com.videoringtoneapp.DialerActivity.Companion  MainActivity -com.videoringtoneapp.DialerActivity.Companion  TAG -com.videoringtoneapp.DialerActivity.Companion  Toast -com.videoringtoneapp.DialerActivity.Companion  Uri -com.videoringtoneapp.DialerActivity.Companion  apply -com.videoringtoneapp.DialerActivity.Companion  java -com.videoringtoneapp.DialerActivity.Companion  listOf (com.videoringtoneapp.VideoRingtoneModule  tryVivoFallbacks (com.videoringtoneapp.VideoRingtoneModule  	withIndex (com.videoringtoneapp.VideoRingtoneModule  listOf 2com.videoringtoneapp.VideoRingtoneModule.Companion  	withIndex 2com.videoringtoneapp.VideoRingtoneModule.Companion  IndexedValue kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  	withIndex kotlin.collections  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  iterator kotlin.collections.Iterable  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  	withIndex kotlin.collections.List  	withIndex kotlin.sequences  	withIndex kotlin.text  Call android.app.Service  Int android.app.Service  VideoRingtoneActivity android.app.Service  VideoRingtoneService android.app.Service  handleIncomingCall android.app.Service  stopVideoRingtone android.app.Service  Callback android.app.Service.Call  Call android.content.Context  Int android.content.Context  VideoRingtoneActivity android.content.Context  VideoRingtoneService android.content.Context  handleIncomingCall android.content.Context  stopVideoRingtone android.content.Context  Callback android.content.Context.Call  Call android.content.ContextWrapper  Int android.content.ContextWrapper  VideoRingtoneActivity android.content.ContextWrapper  VideoRingtoneService android.content.ContextWrapper  handleIncomingCall android.content.ContextWrapper  stopVideoRingtone android.content.ContextWrapper  Callback #android.content.ContextWrapper.Call  Call android.telecom  
InCallService android.telecom  Callback android.telecom.Call  STATE_ACTIVE android.telecom.Call  STATE_DISCONNECTED android.telecom.Call  
STATE_RINGING android.telecom.Call  details android.telecom.Call  registerCallback android.telecom.Call  state android.telecom.Call  unregisterCallback android.telecom.Call  Call android.telecom.Call.Callback  Log android.telecom.Call.Callback  TAG android.telecom.Call.Callback  handleIncomingCall android.telecom.Call.Callback  onStateChanged android.telecom.Call.Callback  stopVideoRingtone android.telecom.Call.Callback  handle android.telecom.Call.Details  Call android.telecom.InCallService  	Exception android.telecom.InCallService  Int android.telecom.InCallService  Intent android.telecom.InCallService  Log android.telecom.InCallService  TAG android.telecom.InCallService  VideoRingtoneActivity android.telecom.InCallService  VideoRingtoneService android.telecom.InCallService  apply android.telecom.InCallService  handleIncomingCall android.telecom.InCallService  java android.telecom.InCallService  onCallAdded android.telecom.InCallService  
onCallRemoved android.telecom.InCallService  stopVideoRingtone android.telecom.InCallService  Callback "android.telecom.InCallService.Call  Call com.videoringtoneapp  
InCallService com.videoringtoneapp  VideoRingtoneInCallService com.videoringtoneapp  handleIncomingCall com.videoringtoneapp  stopVideoRingtone com.videoringtoneapp  Callback com.videoringtoneapp.Call  Call /com.videoringtoneapp.VideoRingtoneInCallService  	Exception /com.videoringtoneapp.VideoRingtoneInCallService  Int /com.videoringtoneapp.VideoRingtoneInCallService  Intent /com.videoringtoneapp.VideoRingtoneInCallService  Log /com.videoringtoneapp.VideoRingtoneInCallService  TAG /com.videoringtoneapp.VideoRingtoneInCallService  VideoRingtoneActivity /com.videoringtoneapp.VideoRingtoneInCallService  VideoRingtoneService /com.videoringtoneapp.VideoRingtoneInCallService  apply /com.videoringtoneapp.VideoRingtoneInCallService  callCallback /com.videoringtoneapp.VideoRingtoneInCallService  handleIncomingCall /com.videoringtoneapp.VideoRingtoneInCallService  java /com.videoringtoneapp.VideoRingtoneInCallService  
startActivity /com.videoringtoneapp.VideoRingtoneInCallService  startForegroundService /com.videoringtoneapp.VideoRingtoneInCallService  startService /com.videoringtoneapp.VideoRingtoneInCallService  stopVideoRingtone /com.videoringtoneapp.VideoRingtoneInCallService  Callback 4com.videoringtoneapp.VideoRingtoneInCallService.Call  Call 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  Intent 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  Log 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  TAG 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  VideoRingtoneActivity 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  VideoRingtoneService 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  apply 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  handleIncomingCall 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  java 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  stopVideoRingtone 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  Builder android.app.Notification  
CATEGORY_CALL android.app.Notification  
PRIORITY_HIGH android.app.Notification  build  android.app.Notification.Builder  setCategory  android.app.Notification.Builder  setContentIntent  android.app.Notification.Builder  setContentText  android.app.Notification.Builder  setContentTitle  android.app.Notification.Builder  setFullScreenIntent  android.app.Notification.Builder  
setOngoing  android.app.Notification.Builder  setPriority  android.app.Notification.Builder  setSmallIcon  android.app.Notification.Builder  AudioAttributes android.app.NotificationChannel  RingtoneManager android.app.NotificationChannel  enableLights android.app.NotificationChannel  enableVibration android.app.NotificationChannel  cancel android.app.NotificationManager  notify android.app.NotificationManager  INCOMING_CALL_CHANNEL_ID android.app.Service  Notification android.app.Service  dismissIncomingCallNotification android.app.Service  INCOMING_CALL_CHANNEL_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  dismissIncomingCallNotification android.content.Context  INCOMING_CALL_CHANNEL_ID android.content.ContextWrapper  Notification android.content.ContextWrapper  dismissIncomingCallNotification android.content.ContextWrapper  dismissIncomingCallNotification android.telecom.Call.Callback  AudioAttributes android.telecom.InCallService  Context android.telecom.InCallService  INCOMING_CALL_CHANNEL_ID android.telecom.InCallService  NOTIFICATION_ID android.telecom.InCallService  Notification android.telecom.InCallService  NotificationChannel android.telecom.InCallService  NotificationManager android.telecom.InCallService  
PendingIntent android.telecom.InCallService  RingtoneManager android.telecom.InCallService  android android.telecom.InCallService  dismissIncomingCallNotification android.telecom.InCallService  onCreate android.telecom.InCallService  INCOMING_CALL_CHANNEL_ID com.videoringtoneapp  dismissIncomingCallNotification com.videoringtoneapp  AudioAttributes /com.videoringtoneapp.VideoRingtoneInCallService  Context /com.videoringtoneapp.VideoRingtoneInCallService  INCOMING_CALL_CHANNEL_ID /com.videoringtoneapp.VideoRingtoneInCallService  NOTIFICATION_ID /com.videoringtoneapp.VideoRingtoneInCallService  Notification /com.videoringtoneapp.VideoRingtoneInCallService  NotificationChannel /com.videoringtoneapp.VideoRingtoneInCallService  NotificationManager /com.videoringtoneapp.VideoRingtoneInCallService  
PendingIntent /com.videoringtoneapp.VideoRingtoneInCallService  RingtoneManager /com.videoringtoneapp.VideoRingtoneInCallService  String /com.videoringtoneapp.VideoRingtoneInCallService  android /com.videoringtoneapp.VideoRingtoneInCallService  createNotificationChannel /com.videoringtoneapp.VideoRingtoneInCallService  dismissIncomingCallNotification /com.videoringtoneapp.VideoRingtoneInCallService  getSystemService /com.videoringtoneapp.VideoRingtoneInCallService  showIncomingCallNotification /com.videoringtoneapp.VideoRingtoneInCallService  AudioAttributes 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  Context 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  INCOMING_CALL_CHANNEL_ID 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  NOTIFICATION_ID 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  Notification 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  NotificationChannel 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  NotificationManager 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  
PendingIntent 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  RingtoneManager 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  android 9com.videoringtoneapp.VideoRingtoneInCallService.Companion  dismissIncomingCallNotification 9com.videoringtoneapp.VideoRingtoneInCallService.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     