1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.videoringtoneapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:5-67
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:5-75
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:22-72
13    <uses-permission android:name="android.permission.CALL_PHONE" />
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:5-69
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:22-66
14    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:5-77
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:22-74
15    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:5-78
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:22-75
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:5-68
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:22-65
17    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:5-75
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:22-72
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:5-66
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:22-63
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:5-80
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:5-81
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:22-78
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:5-75
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:22-72
22
23    <!-- Additional permissions required for dialer app -->
24    <uses-permission android:name="android.permission.READ_CONTACTS" />
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:5-72
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:22-69
25    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:5-73
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:22-70
26    <uses-permission android:name="android.permission.READ_CALL_LOG" />
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:5-72
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:22-69
27    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:5-73
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:22-70
28    <uses-permission android:name="android.permission.ADD_VOICEMAIL" />
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:5-72
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:22-69
29    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:5-77
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:22-74
30    <uses-permission android:name="android.permission.CONTROL_INCALL_EXPERIENCE" />
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:5-84
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:22-81
31    <uses-permission android:name="android.permission.BIND_INCALL_SERVICE" />
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:5-78
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:22-75
32    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:5-111
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:22-79
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
33-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:5-77
33-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:22-74
34    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
34-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:5-88
34-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:22-85
35    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
35-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:28:5-81
35-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:28:22-78
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
36-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:22-76
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
44-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:30:5-127:19
45        android:name="com.videoringtoneapp.MainApplication"
45-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:31:7-38
46        android:allowBackup="false"
46-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:35:7-34
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
48        android:extractNativeLibs="false"
49        android:icon="@mipmap/ic_launcher"
49-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:33:7-41
50        android:label="@string/app_name"
50-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:32:7-39
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:34:7-52
52        android:supportsRtl="true"
52-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:37:7-33
53        android:theme="@style/AppTheme" >
53-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:36:7-38
54        <activity
54-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:38:7-49:18
55            android:name="com.videoringtoneapp.MainActivity"
55-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:39:9-37
56            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
56-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:41:9-118
57            android:exported="true"
57-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:44:9-32
58            android:label="@string/app_name"
58-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:40:9-41
59            android:launchMode="singleTask"
59-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:42:9-40
60            android:windowSoftInputMode="adjustResize" >
60-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:43:9-51
61            <intent-filter>
61-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:9-48:25
62                <action android:name="android.intent.action.MAIN" />
62-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:13-65
62-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:21-62
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:47:13-73
64-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:47:23-70
65            </intent-filter>
66        </activity>
67
68        <!-- Dialer Activity for handling dialer intents -->
69        <activity
69-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:7-83:18
70            android:name="com.videoringtoneapp.DialerActivity"
70-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:53:9-39
71            android:excludeFromRecents="true"
71-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:56:9-42
72            android:exported="true"
72-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:57:9-32
73            android:label="@string/app_name"
73-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:54:9-41
74            android:theme="@android:style/Theme.NoDisplay" >
74-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:55:9-55
75
76            <!-- Handle dialer intents -->
77            <intent-filter android:priority="1000" >
77-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:60:9-63:25
77-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:60:24-47
78                <action android:name="android.intent.action.DIAL" />
78-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:13-65
78-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:21-62
79
80                <category android:name="android.intent.category.DEFAULT" />
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
81            </intent-filter>
82            <intent-filter android:priority="1000" >
82-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:65:9-69:25
82-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:65:24-47
83                <action android:name="android.intent.action.DIAL" />
83-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:13-65
83-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:21-62
84
85                <category android:name="android.intent.category.DEFAULT" />
85-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
85-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
86
87                <data android:scheme="tel" />
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-42
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:19-39
88            </intent-filter>
89            <intent-filter android:priority="1000" >
89-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:9-75:25
89-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:24-47
90                <action android:name="android.intent.action.CALL_PRIVILEGED" />
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:72:13-76
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:72:21-73
91
92                <category android:name="android.intent.category.DEFAULT" />
92-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
92-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
93
94                <data android:scheme="tel" />
94-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-42
94-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:19-39
95            </intent-filter>
96            <intent-filter android:priority="1000" >
96-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:77:9-82:25
96-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:77:24-47
97                <action android:name="android.intent.action.VIEW" />
97-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:78:13-65
97-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:78:21-62
98
99                <category android:name="android.intent.category.DEFAULT" />
99-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
99-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
100                <category android:name="android.intent.category.BROWSABLE" />
100-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:80:13-74
100-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:80:23-71
101
102                <data android:scheme="tel" />
102-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-42
102-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:19-39
103            </intent-filter>
104        </activity>
105
106        <!-- InCallService - REQUIRED for default dialer -->
107        <service
107-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:86:7-99:17
108            android:name="com.videoringtoneapp.VideoRingtoneInCallService"
108-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:87:9-51
109            android:exported="true"
109-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:89:9-32
110            android:permission="android.permission.BIND_INCALL_SERVICE" >
110-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:88:9-68
111            <intent-filter>
111-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:90:9-92:25
112                <action android:name="android.telecom.InCallService" />
112-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:91:11-66
112-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:91:19-63
113            </intent-filter>
114
115            <meta-data
115-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:93:9-95:34
116                android:name="android.telecom.INCLUDE_EXTERNAL_CALLS"
116-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:94:11-64
117                android:value="true" />
117-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:95:11-31
118            <meta-data
118-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:96:9-98:34
119                android:name="android.telecom.INCLUDE_SELF_MANAGED_CALLS"
119-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:97:11-68
120                android:value="true" />
120-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:98:11-31
121        </service>
122
123        <!-- Video Ringtone Service -->
124        <service
124-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:7-106:53
125            android:name="com.videoringtoneapp.VideoRingtoneService"
125-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:103:9-45
126            android:enabled="true"
126-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:104:9-31
127            android:exported="false"
127-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:105:9-33
128            android:foregroundServiceType="phoneCall" />
128-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:106:9-50
129
130        <!-- Call State Receiver -->
131        <receiver
131-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:109:7-116:18
132            android:name="com.videoringtoneapp.CallStateReceiver"
132-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:110:9-42
133            android:enabled="true"
133-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:111:9-31
134            android:exported="true" >
134-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:112:9-32
135            <intent-filter android:priority="1000" >
135-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:113:9-115:25
135-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:113:24-47
136                <action android:name="android.intent.action.PHONE_STATE" />
136-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:114:11-70
136-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:114:19-67
137            </intent-filter>
138        </receiver>
139
140        <!-- Video Ringtone Activity -->
141        <activity
141-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:119:7-126:36
142            android:name="com.videoringtoneapp.VideoRingtoneActivity"
142-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:120:9-46
143            android:excludeFromRecents="true"
143-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:9-42
144            android:exported="false"
144-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:126:9-33
145            android:launchMode="singleTop"
145-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:9-39
146            android:showOnLockScreen="true"
146-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:124:9-40
147            android:theme="@style/VideoRingtoneTheme"
147-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:121:9-50
148            android:turnScreenOn="true" />
148-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:125:9-36
149
150        <provider
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
151            android:name="androidx.startup.InitializationProvider"
151-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
152            android:authorities="com.videoringtoneapp.androidx-startup"
152-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
153            android:exported="false" >
153-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
154            <meta-data
154-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
155                android:name="androidx.emoji2.text.EmojiCompatInitializer"
155-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
156                android:value="androidx.startup" />
156-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
157            <meta-data
157-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
158                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
158-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
159                android:value="androidx.startup" />
159-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
160            <meta-data
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
161                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
162                android:value="androidx.startup" />
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
163        </provider>
164
165        <receiver
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
166            android:name="androidx.profileinstaller.ProfileInstallReceiver"
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
167            android:directBootAware="false"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
168            android:enabled="true"
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
169            android:exported="true"
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
170            android:permission="android.permission.DUMP" >
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
171            <intent-filter>
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
172                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
173            </intent-filter>
174            <intent-filter>
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
175                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
176            </intent-filter>
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
178                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
181                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
182            </intent-filter>
183        </receiver>
184
185        <meta-data
185-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
186            android:name="com.facebook.soloader.enabled"
186-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
187            android:value="false" />
187-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
188    </application>
189
190</manifest>
