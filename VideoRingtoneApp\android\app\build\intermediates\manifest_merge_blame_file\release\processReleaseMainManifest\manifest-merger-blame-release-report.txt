1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.videoringtoneapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:5-67
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:5-75
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:22-72
13    <uses-permission android:name="android.permission.CALL_PHONE" />
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:5-69
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:22-66
14    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:5-77
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:22-74
15    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:5-78
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:22-75
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:5-68
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:22-65
17    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:5-75
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:22-72
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:5-66
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:22-63
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:5-80
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:5-81
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:22-78
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:5-75
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:22-72
22
23    <!-- Additional permissions required for dialer app -->
24    <uses-permission android:name="android.permission.READ_CONTACTS" />
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:5-72
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:22-69
25    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:5-73
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:22-70
26    <uses-permission android:name="android.permission.READ_CALL_LOG" />
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:5-72
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:22-69
27    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:5-73
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:22-70
28    <uses-permission android:name="android.permission.ADD_VOICEMAIL" />
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:5-72
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:22-69
29    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:5-77
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:22-74
30    <uses-permission android:name="android.permission.CONTROL_INCALL_EXPERIENCE" />
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:5-84
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:22-81
31    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:5-111
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:22-79
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:5-77
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:22-74
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
33-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:5-88
33-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:22-85
34    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
34-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:5-81
34-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:22-78
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
35-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:22-76
36
37    <permission
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
38        android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
42
43    <application
43-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:29:5-136:19
44        android:name="com.videoringtoneapp.MainApplication"
44-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:30:7-38
45        android:allowBackup="false"
45-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:34:7-34
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
47        android:extractNativeLibs="false"
48        android:icon="@mipmap/ic_launcher"
48-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:32:7-41
49        android:label="@string/app_name"
49-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:31:7-39
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:33:7-52
51        android:supportsRtl="true"
51-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:36:7-33
52        android:theme="@style/AppTheme" >
52-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:35:7-38
53        <activity
53-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:37:7-74:18
54            android:name="com.videoringtoneapp.MainActivity"
54-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:38:9-37
55            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
55-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:40:9-118
56            android:exported="true"
56-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:43:9-32
57            android:label="@string/app_name"
57-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:39:9-41
58            android:launchMode="singleTask"
58-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:41:9-40
59            android:windowSoftInputMode="adjustResize" >
59-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:42:9-51
60            <intent-filter>
60-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:44:9-47:25
61                <action android:name="android.intent.action.MAIN" />
61-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:13-65
61-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:21-62
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:13-73
63-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:23-70
64            </intent-filter>
65
66            <!-- Required intent filters for default dialer -->
67            <intent-filter>
67-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:50:9-53:25
68                <action android:name="android.intent.action.DIAL" />
68-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
68-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
69
70                <category android:name="android.intent.category.DEFAULT" />
70-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
70-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
71            </intent-filter>
72            <intent-filter>
72-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:55:9-59:25
73                <action android:name="android.intent.action.DIAL" />
73-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
73-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
74
75                <category android:name="android.intent.category.DEFAULT" />
75-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
75-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
76
77                <data android:scheme="tel" />
77-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
77-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
78            </intent-filter>
79            <intent-filter>
79-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:9-65:25
80                <action android:name="android.intent.action.CALL_PRIVILEGED" />
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-76
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:21-73
81
82                <category android:name="android.intent.category.DEFAULT" />
82-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
82-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
83
84                <data android:scheme="tel" />
84-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
84-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
85            </intent-filter>
86            <intent-filter>
86-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:67:9-73:25
87                <action android:name="android.intent.action.VIEW" />
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-65
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:21-62
88                <action android:name="android.intent.action.DIAL" />
88-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
88-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
89
90                <category android:name="android.intent.category.DEFAULT" />
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
91                <category android:name="android.intent.category.BROWSABLE" />
91-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:13-74
91-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:23-71
92
93                <data android:scheme="tel" />
93-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
93-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
94            </intent-filter>
95        </activity>
96
97        <!-- Dialer Activity for handling dialer intents -->
98        <activity
98-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:77:7-108:18
99            android:name="com.videoringtoneapp.DialerActivity"
99-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:78:9-39
100            android:excludeFromRecents="true"
100-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:81:9-42
101            android:exported="true"
101-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:82:9-32
102            android:label="@string/app_name"
102-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:79:9-41
103            android:theme="@android:style/Theme.NoDisplay" >
103-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:80:9-55
104
105            <!-- Handle dialer intents -->
106            <intent-filter android:priority="1000" >
106-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:50:9-53:25
106-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:85:24-47
107                <action android:name="android.intent.action.DIAL" />
107-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
107-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
108
109                <category android:name="android.intent.category.DEFAULT" />
109-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
109-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
110            </intent-filter>
111            <intent-filter android:priority="1000" >
111-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:55:9-59:25
111-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:90:24-47
112                <action android:name="android.intent.action.DIAL" />
112-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
112-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
113
114                <category android:name="android.intent.category.DEFAULT" />
114-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
114-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
115
116                <data android:scheme="tel" />
116-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
116-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
117            </intent-filter>
118            <intent-filter android:priority="1000" >
118-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:9-65:25
118-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:96:24-47
119                <action android:name="android.intent.action.CALL_PRIVILEGED" />
119-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-76
119-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:21-73
120
121                <category android:name="android.intent.category.DEFAULT" />
121-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
121-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
122
123                <data android:scheme="tel" />
123-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
123-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
124            </intent-filter>
125            <intent-filter android:priority="1000" >
125-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:9-107:25
125-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:24-47
126                <action android:name="android.intent.action.VIEW" />
126-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-65
126-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:21-62
127
128                <category android:name="android.intent.category.DEFAULT" />
128-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
128-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
129                <category android:name="android.intent.category.BROWSABLE" />
129-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:13-74
129-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:23-71
130
131                <data android:scheme="tel" />
131-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
131-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
132            </intent-filter>
133        </activity>
134
135        <!-- Video Ringtone Service -->
136        <service
136-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:111:7-115:53
137            android:name="com.videoringtoneapp.VideoRingtoneService"
137-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:112:9-45
138            android:enabled="true"
138-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:113:9-31
139            android:exported="false"
139-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:114:9-33
140            android:foregroundServiceType="phoneCall" />
140-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:115:9-50
141
142        <!-- Call State Receiver -->
143        <receiver
143-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:118:7-125:18
144            android:name="com.videoringtoneapp.CallStateReceiver"
144-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:119:9-42
145            android:enabled="true"
145-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:120:9-31
146            android:exported="true" >
146-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:121:9-32
147            <intent-filter android:priority="1000" >
147-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:9-124:25
147-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:24-47
148                <action android:name="android.intent.action.PHONE_STATE" />
148-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:11-70
148-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:19-67
149            </intent-filter>
150        </receiver>
151
152        <!-- Video Ringtone Activity -->
153        <activity
153-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:128:7-135:36
154            android:name="com.videoringtoneapp.VideoRingtoneActivity"
154-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:129:9-46
155            android:excludeFromRecents="true"
155-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:132:9-42
156            android:exported="false"
156-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:135:9-33
157            android:launchMode="singleTop"
157-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:131:9-39
158            android:showOnLockScreen="true"
158-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:133:9-40
159            android:theme="@style/VideoRingtoneTheme"
159-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:130:9-50
160            android:turnScreenOn="true" />
160-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:134:9-36
161
162        <provider
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
163            android:name="androidx.startup.InitializationProvider"
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
164            android:authorities="com.videoringtoneapp.androidx-startup"
164-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
165            android:exported="false" >
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
166            <meta-data
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.emoji2.text.EmojiCompatInitializer"
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
168                android:value="androidx.startup" />
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
169            <meta-data
169-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
170-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
171                android:value="androidx.startup" />
171-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
174                android:value="androidx.startup" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
175        </provider>
176
177        <receiver
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
178            android:name="androidx.profileinstaller.ProfileInstallReceiver"
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
179            android:directBootAware="false"
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
180            android:enabled="true"
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
181            android:exported="true"
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
182            android:permission="android.permission.DUMP" >
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
184                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
187                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
188            </intent-filter>
189            <intent-filter>
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
190                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
191            </intent-filter>
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
193                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
194            </intent-filter>
195        </receiver>
196
197        <meta-data
197-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
198            android:name="com.facebook.soloader.enabled"
198-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
199            android:value="false" />
199-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
200    </application>
201
202</manifest>
