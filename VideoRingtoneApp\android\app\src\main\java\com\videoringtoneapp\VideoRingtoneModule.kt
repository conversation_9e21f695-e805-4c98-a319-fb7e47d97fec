package com.videoringtoneapp

import android.Manifest
import android.app.Activity
import android.app.role.RoleManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.provider.Settings
import android.telecom.TelecomManager
import android.util.Log
import androidx.core.content.ContextCompat
import com.facebook.react.bridge.ActivityEventListener
import com.facebook.react.bridge.BaseActivityEventListener
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.WritableMap
import com.facebook.react.bridge.WritableNativeMap
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

class VideoRingtoneModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    companion object {
        private const val TAG = "VideoRingtoneModule"
        private const val VIDEO_RINGTONE_FILENAME = "video_ringtone.mp4"
        private const val PREFS_NAME = "video_ringtone_prefs"
        private const val PREF_VIDEO_PATH = "video_path"
        private const val PICK_VIDEO_REQUEST = 1001
    }

    private var pickVideoPromise: Promise? = null

    private val activityEventListener = object : BaseActivityEventListener() {
        override fun onActivityResult(activity: Activity?, requestCode: Int, resultCode: Int, intent: Intent?) {
            if (requestCode == PICK_VIDEO_REQUEST) {
                handleVideoPickerResult(resultCode, intent)
            }
        }
    }

    init {
        reactApplicationContext.addActivityEventListener(activityEventListener)
    }

    override fun getName(): String = "VideoRingtoneModule"

    @ReactMethod
    fun checkPermissions(promise: Promise) {
        try {
            val permissions = WritableNativeMap()
            val context = reactApplicationContext

            // Check phone state permission
            permissions.putBoolean(
                "READ_PHONE_STATE",
                ContextCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED
            )

            // Check system alert window permission
            permissions.putBoolean(
                "SYSTEM_ALERT_WINDOW",
                Settings.canDrawOverlays(context)
            )

            // Check call phone permission
            permissions.putBoolean(
                "CALL_PHONE",
                ContextCompat.checkSelfPermission(context, Manifest.permission.CALL_PHONE) == PackageManager.PERMISSION_GRANTED
            )

            // Check answer phone calls permission (Android 8+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                permissions.putBoolean(
                    "ANSWER_PHONE_CALLS",
                    ContextCompat.checkSelfPermission(context, Manifest.permission.ANSWER_PHONE_CALLS) == PackageManager.PERMISSION_GRANTED
                )
            } else {
                permissions.putBoolean("ANSWER_PHONE_CALLS", true)
            }

            // Check storage permissions (Android 13+ vs legacy)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ uses granular media permissions
                permissions.putBoolean(
                    "READ_MEDIA_VIDEO",
                    ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_VIDEO) == PackageManager.PERMISSION_GRANTED
                )
                // Legacy permission not needed on Android 13+
                permissions.putBoolean("READ_EXTERNAL_STORAGE", true)
            } else {
                // Android 12 and below use READ_EXTERNAL_STORAGE
                permissions.putBoolean(
                    "READ_EXTERNAL_STORAGE",
                    ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                )
                // Media permission not available on older versions
                permissions.putBoolean("READ_MEDIA_VIDEO", true)
            }

            // Check if app is default dialer (required for call management on newer Android)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val telecomManager = context.getSystemService(Context.TELECOM_SERVICE) as TelecomManager
                permissions.putBoolean(
                    "DEFAULT_DIALER",
                    context.packageName == telecomManager.defaultDialerPackage
                )
            } else {
                permissions.putBoolean("DEFAULT_DIALER", true)
            }

            Log.d(TAG, "Permission check results: $permissions")
            promise.resolve(permissions)

        } catch (e: Exception) {
            Log.e(TAG, "Error checking permissions", e)
            promise.reject("PERMISSION_CHECK_ERROR", e.message)
        }
    }

    @ReactMethod
    fun requestStoragePermission(promise: Promise) {
        try {
            val context = reactApplicationContext

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ - Check if READ_MEDIA_VIDEO permission is granted
                if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_VIDEO) != PackageManager.PERMISSION_GRANTED) {
                    // Open app settings for manual permission grant
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.parse("package:${context.packageName}")
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                    context.startActivity(intent)
                    promise.resolve(true)
                } else {
                    promise.resolve(false) // Already granted
                }
            } else {
                // Android 12 and below - Check READ_EXTERNAL_STORAGE
                if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                    // Open app settings for manual permission grant
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.parse("package:${context.packageName}")
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    }
                    context.startActivity(intent)
                    promise.resolve(true)
                } else {
                    promise.resolve(false) // Already granted
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting storage permission", e)
            promise.reject("STORAGE_PERMISSION_ERROR", e.message)
        }
    }

    @ReactMethod
    fun requestSystemAlertWindowPermission(promise: Promise) {
        try {
            if (!Settings.canDrawOverlays(reactApplicationContext)) {
                val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                    data = Uri.parse("package:${reactApplicationContext.packageName}")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                reactApplicationContext.startActivity(intent)
                promise.resolve(true)
            } else {
                promise.resolve(false) // Already granted
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting system alert window permission", e)
            promise.reject("PERMISSION_REQUEST_ERROR", e.message)
        }
    }

    @ReactMethod
    fun requestDefaultDialerRole(promise: Promise) {
        try {
            val context = reactApplicationContext

            // Check if already default dialer
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val telecomManager = context.getSystemService(Context.TELECOM_SERVICE) as TelecomManager
                if (context.packageName == telecomManager.defaultDialerPackage) {
                    promise.resolve(false) // Already granted
                    return
                }
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Android 10+ - Use RoleManager
                try {
                    val roleManager = context.getSystemService(Context.ROLE_SERVICE) as RoleManager
                    if (!roleManager.isRoleHeld(RoleManager.ROLE_DIALER)) {
                        val intent = roleManager.createRequestRoleIntent(RoleManager.ROLE_DIALER)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        context.startActivity(intent)
                        promise.resolve(true)
                    } else {
                        promise.resolve(false) // Already granted
                    }
                } catch (e: Exception) {
                    Log.w(TAG, "RoleManager failed, falling back to TelecomManager", e)
                    // Fallback to TelecomManager for devices that don't support RoleManager properly
                    fallbackToTelecomManager(context, promise)
                }
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6-9 - Use TelecomManager
                fallbackToTelecomManager(context, promise)
            } else {
                promise.resolve(false) // Not needed on older versions
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting default dialer role", e)
            promise.reject("DIALER_REQUEST_ERROR", e.message)
        }
    }

    private fun fallbackToTelecomManager(context: ReactApplicationContext, promise: Promise) {
        try {
            val intent = Intent(TelecomManager.ACTION_CHANGE_DEFAULT_DIALER).apply {
                putExtra(TelecomManager.EXTRA_CHANGE_DEFAULT_DIALER_PACKAGE_NAME, context.packageName)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "TelecomManager fallback failed", e)
            // Final fallback - open app settings
            try {
                val settingsIntent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.parse("package:${context.packageName}")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(settingsIntent)
                promise.resolve(true)
            } catch (settingsError: Exception) {
                Log.e(TAG, "Settings fallback failed", settingsError)
                promise.reject("DIALER_REQUEST_ERROR", "Unable to open dialer settings: ${settingsError.message}")
            }
        }
    }

    @ReactMethod
    fun openAppSettings(promise: Promise) {
        try {
            val context = reactApplicationContext
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${context.packageName}")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening app settings", e)
            promise.reject("SETTINGS_ERROR", e.message)
        }
    }

    @ReactMethod
    fun getDeviceInfo(promise: Promise) {
        try {
            val deviceInfo = WritableNativeMap()
            deviceInfo.putString("manufacturer", Build.MANUFACTURER)
            deviceInfo.putString("brand", Build.BRAND)
            deviceInfo.putString("model", Build.MODEL)
            deviceInfo.putInt("sdkVersion", Build.VERSION.SDK_INT)
            deviceInfo.putString("release", Build.VERSION.RELEASE)

            // Check if it's a Vivo device
            val isVivo = Build.MANUFACTURER.lowercase().contains("vivo") ||
                        Build.BRAND.lowercase().contains("vivo")
            deviceInfo.putBoolean("isVivo", isVivo)

            promise.resolve(deviceInfo)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting device info", e)
            promise.reject("DEVICE_INFO_ERROR", e.message)
        }
    }

    @ReactMethod
    fun testVideoRingtone(promise: Promise) {
        try {
            // Start the video ringtone service for testing
            val serviceIntent = Intent(reactApplicationContext, VideoRingtoneService::class.java).apply {
                action = VideoRingtoneService.ACTION_START_VIDEO_RINGTONE
                putExtra("phone_number", "Test Call")
            }
            reactApplicationContext.startForegroundService(serviceIntent)

            // Start the video ringtone activity for testing
            val activityIntent = Intent(reactApplicationContext, VideoRingtoneActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_SINGLE_TOP
                putExtra("phone_number", "Test Call")
            }
            reactApplicationContext.startActivity(activityIntent)

            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error testing video ringtone", e)
            promise.reject("TEST_ERROR", e.message)
        }
    }

    @ReactMethod
    fun stopTestVideoRingtone(promise: Promise) {
        try {
            // Stop the video ringtone service
            val serviceIntent = Intent(reactApplicationContext, VideoRingtoneService::class.java).apply {
                action = VideoRingtoneService.ACTION_STOP_VIDEO_RINGTONE
            }
            reactApplicationContext.startService(serviceIntent)

            // Finish the video ringtone activity
            val activityIntent = Intent(reactApplicationContext, VideoRingtoneActivity::class.java).apply {
                action = VideoRingtoneActivity.ACTION_FINISH_ACTIVITY
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            reactApplicationContext.startActivity(activityIntent)

            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping test video ringtone", e)
            promise.reject("STOP_TEST_ERROR", e.message)
        }
    }

    @ReactMethod
    fun getAppInfo(promise: Promise) {
        try {
            val info = WritableNativeMap()
            val context = reactApplicationContext

            info.putString("packageName", context.packageName)
            info.putString("versionName", context.packageManager.getPackageInfo(context.packageName, 0).versionName)
            info.putInt("versionCode", context.packageManager.getPackageInfo(context.packageName, 0).versionCode)
            info.putInt("targetSdkVersion", context.applicationInfo.targetSdkVersion)
            info.putInt("androidVersion", Build.VERSION.SDK_INT)
            info.putString("androidRelease", Build.VERSION.RELEASE)

            promise.resolve(info)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting app info", e)
            promise.reject("APP_INFO_ERROR", e.message)
        }
    }

    @ReactMethod
    fun setVideoRingtone(videoUri: String, fileName: String, promise: Promise) {
        try {
            val context = reactApplicationContext
            val uri = Uri.parse(videoUri)

            // Create internal storage directory for video files
            val videoDir = File(context.filesDir, "videos")
            if (!videoDir.exists()) {
                videoDir.mkdirs()
            }

            // Copy video file to internal storage
            val videoFile = File(videoDir, VIDEO_RINGTONE_FILENAME)
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            val outputStream = FileOutputStream(videoFile)

            inputStream?.use { input ->
                outputStream.use { output ->
                    input.copyTo(output)
                }
            }

            // Save video path in preferences
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit()
                .putString(PREF_VIDEO_PATH, fileName)
                .apply()

            Log.d(TAG, "Video ringtone set: $fileName")
            promise.resolve(true)

        } catch (e: Exception) {
            Log.e(TAG, "Error setting video ringtone", e)
            promise.reject("SET_VIDEO_ERROR", e.message)
        }
    }

    @ReactMethod
    fun getCurrentVideoPath(promise: Promise) {
        try {
            val context = reactApplicationContext
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val videoPath = prefs.getString(PREF_VIDEO_PATH, null)

            // Check if video file actually exists
            if (videoPath != null) {
                val videoFile = File(context.filesDir, "videos/$VIDEO_RINGTONE_FILENAME")
                if (videoFile.exists()) {
                    promise.resolve(videoPath)
                } else {
                    // File doesn't exist, clear preference
                    prefs.edit().remove(PREF_VIDEO_PATH).apply()
                    promise.resolve(null)
                }
            } else {
                promise.resolve(null)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error getting current video path", e)
            promise.reject("GET_VIDEO_ERROR", e.message)
        }
    }

    @ReactMethod
    fun removeVideoRingtone(promise: Promise) {
        try {
            val context = reactApplicationContext

            // Remove video file
            val videoFile = File(context.filesDir, "videos/$VIDEO_RINGTONE_FILENAME")
            if (videoFile.exists()) {
                videoFile.delete()
            }

            // Clear preference
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit().remove(PREF_VIDEO_PATH).apply()

            Log.d(TAG, "Video ringtone removed")
            promise.resolve(true)

        } catch (e: Exception) {
            Log.e(TAG, "Error removing video ringtone", e)
            promise.reject("REMOVE_VIDEO_ERROR", e.message)
        }
    }

    @ReactMethod
    fun getVideoRingtoneFile(promise: Promise) {
        try {
            val context = reactApplicationContext
            val videoFile = File(context.filesDir, "videos/$VIDEO_RINGTONE_FILENAME")

            if (videoFile.exists()) {
                promise.resolve(videoFile.absolutePath)
            } else {
                promise.resolve(null)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error getting video ringtone file", e)
            promise.reject("GET_VIDEO_FILE_ERROR", e.message)
        }
    }

    @ReactMethod
    fun pickVideoFile(promise: Promise) {
        try {
            val activity = currentActivity
            if (activity == null) {
                promise.reject("NO_ACTIVITY", "Activity not available")
                return
            }

            pickVideoPromise = promise

            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                type = "video/*"
                addCategory(Intent.CATEGORY_OPENABLE)
                putExtra(Intent.EXTRA_MIME_TYPES, arrayOf(
                    "video/mp4",
                    "video/3gpp",
                    "video/webm",
                    "video/mkv",
                    "video/avi",
                    "video/mov"
                ))
            }

            activity.startActivityForResult(
                Intent.createChooser(intent, "Select Video File"),
                PICK_VIDEO_REQUEST
            )

        } catch (e: Exception) {
            Log.e(TAG, "Error picking video file", e)
            promise.reject("PICK_VIDEO_ERROR", e.message)
        }
    }

    private fun handleVideoPickerResult(resultCode: Int, intent: Intent?) {
        val promise = pickVideoPromise
        pickVideoPromise = null

        if (promise == null) {
            Log.w(TAG, "No promise available for video picker result")
            return
        }

        try {
            if (resultCode == Activity.RESULT_OK && intent != null) {
                val uri = intent.data
                if (uri != null) {
                    val fileInfo = getFileInfo(uri)
                    if (fileInfo != null) {
                        val result = WritableNativeMap().apply {
                            putBoolean("success", true)
                            putMap("file", fileInfo)
                        }
                        promise.resolve(result)
                    } else {
                        promise.reject("FILE_INFO_ERROR", "Could not get file information")
                    }
                } else {
                    promise.reject("NO_URI", "No file URI received")
                }
            } else {
                val result = WritableNativeMap().apply {
                    putBoolean("success", false)
                    putString("error", "User cancelled or no file selected")
                }
                promise.resolve(result)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling video picker result", e)
            promise.reject("PICKER_RESULT_ERROR", e.message)
        }
    }

    private fun getFileInfo(uri: Uri): WritableMap? {
        try {
            val context = reactApplicationContext
            val cursor: Cursor? = context.contentResolver.query(
                uri, null, null, null, null
            )

            cursor?.use {
                if (it.moveToFirst()) {
                    val nameIndex = it.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME)
                    val sizeIndex = it.getColumnIndex(MediaStore.MediaColumns.SIZE)
                    val mimeIndex = it.getColumnIndex(MediaStore.MediaColumns.MIME_TYPE)

                    val name = if (nameIndex >= 0) it.getString(nameIndex) else "Unknown"
                    val size = if (sizeIndex >= 0) it.getLong(sizeIndex) else 0L
                    val mimeType = if (mimeIndex >= 0) it.getString(mimeIndex) else "video/*"

                    return WritableNativeMap().apply {
                        putString("uri", uri.toString())
                        putString("name", name ?: "Unknown")
                        putDouble("size", size.toDouble())
                        putString("type", mimeType ?: "video/*")
                    }
                }
            }

            // Fallback if cursor doesn't work
            return WritableNativeMap().apply {
                putString("uri", uri.toString())
                putString("name", "Selected Video")
                putDouble("size", 0.0)
                putString("type", "video/*")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error getting file info", e)
            return null
        }
    }
}
