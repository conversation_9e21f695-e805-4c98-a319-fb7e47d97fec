1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.videoringtoneapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:5-67
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:5-75
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:22-72
13    <uses-permission android:name="android.permission.CALL_PHONE" />
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:5-69
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:22-66
14    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:5-77
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:22-74
15    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:5-78
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:22-75
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:5-68
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:22-65
17    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:5-75
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:22-72
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:5-66
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:22-63
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:5-80
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:5-81
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:22-78
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- Additional permissions required for dialer app -->
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:5-75
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:22-72
22    <uses-permission android:name="android.permission.READ_CONTACTS" />
22-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:5-72
22-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:22-69
23    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
23-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:5-73
23-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:22-70
24    <uses-permission android:name="android.permission.READ_CALL_LOG" />
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:5-72
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:22-69
25    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:5-73
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:22-70
26    <uses-permission android:name="android.permission.ADD_VOICEMAIL" />
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:5-72
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:22-69
27    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:5-77
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:22-74
28    <uses-permission android:name="android.permission.CONTROL_INCALL_EXPERIENCE" />
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:5-84
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:22-81
29    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:5-111
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:22-79
30    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:5-77
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:22-74
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:5-88
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:22-85
32    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:5-81
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:22-78
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
33-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:22-76
34
35    <permission
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
36        android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
40
41    <application
41-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:29:5-136:19
42        android:name="com.videoringtoneapp.MainApplication"
42-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:30:7-38
43        android:allowBackup="false"
43-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:34:7-34
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
45        android:debuggable="true"
46        android:extractNativeLibs="false"
47        android:icon="@mipmap/ic_launcher"
47-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:32:7-41
48        android:label="@string/app_name"
48-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:31:7-39
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:33:7-52
50        android:supportsRtl="true"
50-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:36:7-33
51        android:theme="@style/AppTheme"
51-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:35:7-38
52        android:usesCleartextTraffic="true" >
52-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:6:9-44
53        <activity
53-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:37:7-74:18
54            android:name="com.videoringtoneapp.MainActivity"
54-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:38:9-37
55            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
55-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:40:9-118
56            android:exported="true"
56-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:43:9-32
57            android:label="@string/app_name"
57-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:39:9-41
58            android:launchMode="singleTask"
58-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:41:9-40
59            android:windowSoftInputMode="adjustResize" >
59-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:42:9-51
60            <intent-filter>
60-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:44:9-47:25
61                <action android:name="android.intent.action.MAIN" />
61-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:13-65
61-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:21-62
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:13-73
63-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:23-70
64            </intent-filter>
65
66            <!-- Required intent filters for default dialer -->
67            <intent-filter>
67-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:50:9-53:25
68                <action android:name="android.intent.action.DIAL" />
68-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
68-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
69
70                <category android:name="android.intent.category.DEFAULT" />
70-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
70-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
71            </intent-filter>
72            <intent-filter>
72-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:55:9-59:25
73                <action android:name="android.intent.action.DIAL" />
73-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
73-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
74
75                <category android:name="android.intent.category.DEFAULT" />
75-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
75-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
76
77                <data android:scheme="tel" />
77-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
77-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
78            </intent-filter>
79            <intent-filter>
79-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:9-65:25
80                <action android:name="android.intent.action.CALL_PRIVILEGED" />
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-76
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:21-73
81
82                <category android:name="android.intent.category.DEFAULT" />
82-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
82-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
83
84                <data android:scheme="tel" />
84-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
84-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
85            </intent-filter>
86            <intent-filter>
86-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:67:9-73:25
87                <action android:name="android.intent.action.VIEW" />
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-65
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:21-62
88                <action android:name="android.intent.action.DIAL" />
88-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
88-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
89
90                <category android:name="android.intent.category.DEFAULT" />
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
91                <category android:name="android.intent.category.BROWSABLE" />
91-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:13-74
91-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:23-71
92
93                <data android:scheme="tel" />
93-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
93-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
94            </intent-filter>
95        </activity> <!-- Dialer Activity for handling dialer intents -->
96        <activity
96-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:77:7-108:18
97            android:name="com.videoringtoneapp.DialerActivity"
97-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:78:9-39
98            android:excludeFromRecents="true"
98-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:81:9-42
99            android:exported="true"
99-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:82:9-32
100            android:label="@string/app_name"
100-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:79:9-41
101            android:theme="@android:style/Theme.NoDisplay" >
101-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:80:9-55
102
103            <!-- Handle dialer intents -->
104            <intent-filter android:priority="1000" >
104-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:50:9-53:25
105                <action android:name="android.intent.action.DIAL" />
105-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
105-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
106
107                <category android:name="android.intent.category.DEFAULT" />
107-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
107-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
108            </intent-filter>
109            <intent-filter android:priority="1000" >
109-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:55:9-59:25
110                <action android:name="android.intent.action.DIAL" />
110-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:13-65
110-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:51:21-62
111
112                <category android:name="android.intent.category.DEFAULT" />
112-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
112-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
113
114                <data android:scheme="tel" />
114-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
114-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
115            </intent-filter>
116            <intent-filter android:priority="1000" >
116-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:9-65:25
117                <action android:name="android.intent.action.CALL_PRIVILEGED" />
117-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-76
117-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:21-73
118
119                <category android:name="android.intent.category.DEFAULT" />
119-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
119-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
120
121                <data android:scheme="tel" />
121-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
121-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
122            </intent-filter>
123            <intent-filter android:priority="1000" >
123-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:9-107:25
123-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:24-47
124                <action android:name="android.intent.action.VIEW" />
124-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-65
124-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:21-62
125
126                <category android:name="android.intent.category.DEFAULT" />
126-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:13-72
126-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:23-69
127                <category android:name="android.intent.category.BROWSABLE" />
127-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:13-74
127-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:23-71
128
129                <data android:scheme="tel" />
129-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:13-42
129-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:58:19-39
130            </intent-filter>
131        </activity> <!-- Video Ringtone Service -->
132        <service
132-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:111:7-115:53
133            android:name="com.videoringtoneapp.VideoRingtoneService"
133-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:112:9-45
134            android:enabled="true"
134-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:113:9-31
135            android:exported="false"
135-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:114:9-33
136            android:foregroundServiceType="phoneCall" /> <!-- Call State Receiver -->
136-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:115:9-50
137        <receiver
137-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:118:7-125:18
138            android:name="com.videoringtoneapp.CallStateReceiver"
138-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:119:9-42
139            android:enabled="true"
139-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:120:9-31
140            android:exported="true" >
140-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:121:9-32
141            <intent-filter android:priority="1000" >
141-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:9-124:25
141-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:24-47
142                <action android:name="android.intent.action.PHONE_STATE" />
142-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:11-70
142-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:19-67
143            </intent-filter>
144        </receiver> <!-- Video Ringtone Activity -->
145        <activity
145-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:128:7-135:36
146            android:name="com.videoringtoneapp.VideoRingtoneActivity"
146-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:129:9-46
147            android:excludeFromRecents="true"
147-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:132:9-42
148            android:exported="false"
148-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:135:9-33
149            android:launchMode="singleTop"
149-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:131:9-39
150            android:showOnLockScreen="true"
150-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:133:9-40
151            android:theme="@style/VideoRingtoneTheme"
151-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:130:9-50
152            android:turnScreenOn="true" />
152-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:134:9-36
153        <activity
153-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
154            android:name="com.facebook.react.devsupport.DevSettingsActivity"
154-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
155            android:exported="false" />
155-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
156
157        <provider
157-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
158            android:name="androidx.startup.InitializationProvider"
158-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
159            android:authorities="com.videoringtoneapp.androidx-startup"
159-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
160            android:exported="false" >
160-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
161            <meta-data
161-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
162                android:name="androidx.emoji2.text.EmojiCompatInitializer"
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
163                android:value="androidx.startup" />
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
164            <meta-data
164-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
165                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
165-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
166                android:value="androidx.startup" />
166-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
167            <meta-data
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
168                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
169                android:value="androidx.startup" />
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
170        </provider>
171
172        <receiver
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
173            android:name="androidx.profileinstaller.ProfileInstallReceiver"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
174            android:directBootAware="false"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
175            android:enabled="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
176            android:exported="true"
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
177            android:permission="android.permission.DUMP" >
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
178            <intent-filter>
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
179                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
180            </intent-filter>
181            <intent-filter>
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
182                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
183            </intent-filter>
184            <intent-filter>
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
185                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
186            </intent-filter>
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
188                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
189            </intent-filter>
190        </receiver>
191
192        <meta-data
192-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
193            android:name="com.facebook.soloader.enabled"
193-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
194            android:value="false" />
194-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
195    </application>
196
197</manifest>
