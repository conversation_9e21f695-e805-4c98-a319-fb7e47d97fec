1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.videoringtoneapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:5-67
11-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:5-75
12-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:5:22-72
13    <uses-permission android:name="android.permission.CALL_PHONE" />
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:5-69
13-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:6:22-66
14    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:5-77
14-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:7:22-74
15    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:5-78
15-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:8:22-75
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:5-68
16-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:9:22-65
17    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:5-75
17-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:10:22-72
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:5-66
18-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:11:22-63
19    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:5-80
19-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:12:22-77
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:5-81
20-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:13:22-78
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- Additional permissions required for dialer app -->
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:5-75
21-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:14:22-72
22    <uses-permission android:name="android.permission.READ_CONTACTS" />
22-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:5-72
22-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:17:22-69
23    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
23-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:5-73
23-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:18:22-70
24    <uses-permission android:name="android.permission.READ_CALL_LOG" />
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:5-72
24-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:19:22-69
25    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:5-73
25-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:20:22-70
26    <uses-permission android:name="android.permission.ADD_VOICEMAIL" />
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:5-72
26-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:21:22-69
27    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:5-77
27-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:22:22-74
28    <uses-permission android:name="android.permission.CONTROL_INCALL_EXPERIENCE" />
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:5-84
28-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:23:22-81
29    <uses-permission android:name="android.permission.BIND_INCALL_SERVICE" />
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:5-78
29-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:24:22-75
30    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:5-111
30-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:25:22-79
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:5-77
31-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:26:22-74
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:5-88
32-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:27:22-85
33    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
33-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:28:5-81
33-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:28:22-78
34    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
34-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:5-79
34-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b474b53b9ec4cd4129a65d104ac92e76\transformed\media3-common-1.4.1\AndroidManifest.xml:22:22-76
35
36    <permission
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
37        android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.videoringtoneapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
41
42    <application
42-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:30:5-127:19
43        android:name="com.videoringtoneapp.MainApplication"
43-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:31:7-38
44        android:allowBackup="false"
44-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:35:7-34
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:icon="@mipmap/ic_launcher"
48-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:33:7-41
49        android:label="@string/app_name"
49-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:32:7-39
50        android:roundIcon="@mipmap/ic_launcher_round"
50-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:34:7-52
51        android:supportsRtl="true"
51-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:37:7-33
52        android:theme="@style/AppTheme"
52-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:36:7-38
53        android:usesCleartextTraffic="true" >
53-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\debug\AndroidManifest.xml:6:9-44
54        <activity
54-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:38:7-49:18
55            android:name="com.videoringtoneapp.MainActivity"
55-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:39:9-37
56            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
56-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:41:9-118
57            android:exported="true"
57-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:44:9-32
58            android:label="@string/app_name"
58-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:40:9-41
59            android:launchMode="singleTask"
59-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:42:9-40
60            android:windowSoftInputMode="adjustResize" >
60-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:43:9-51
61            <intent-filter>
61-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:45:9-48:25
62                <action android:name="android.intent.action.MAIN" />
62-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:13-65
62-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:46:21-62
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:47:13-73
64-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:47:23-70
65            </intent-filter>
66        </activity> <!-- Dialer Activity for handling dialer intents -->
67        <activity
67-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:52:7-83:18
68            android:name="com.videoringtoneapp.DialerActivity"
68-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:53:9-39
69            android:excludeFromRecents="true"
69-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:56:9-42
70            android:exported="true"
70-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:57:9-32
71            android:label="@string/app_name"
71-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:54:9-41
72            android:theme="@android:style/Theme.NoDisplay" >
72-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:55:9-55
73
74            <!-- Handle dialer intents -->
75            <intent-filter android:priority="1000" >
75-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:60:9-63:25
75-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:60:24-47
76                <action android:name="android.intent.action.DIAL" />
76-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:13-65
76-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:21-62
77
78                <category android:name="android.intent.category.DEFAULT" />
78-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
78-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
79            </intent-filter>
80            <intent-filter android:priority="1000" >
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:65:9-69:25
80-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:65:24-47
81                <action android:name="android.intent.action.DIAL" />
81-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:13-65
81-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:61:21-62
82
83                <category android:name="android.intent.category.DEFAULT" />
83-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
83-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
84
85                <data android:scheme="tel" />
85-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-42
85-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:19-39
86            </intent-filter>
87            <intent-filter android:priority="1000" >
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:9-75:25
87-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:71:24-47
88                <action android:name="android.intent.action.CALL_PRIVILEGED" />
88-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:72:13-76
88-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:72:21-73
89
90                <category android:name="android.intent.category.DEFAULT" />
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
90-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
91
92                <data android:scheme="tel" />
92-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-42
92-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:19-39
93            </intent-filter>
94            <intent-filter android:priority="1000" >
94-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:77:9-82:25
94-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:77:24-47
95                <action android:name="android.intent.action.VIEW" />
95-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:78:13-65
95-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:78:21-62
96
97                <category android:name="android.intent.category.DEFAULT" />
97-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:13-72
97-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:62:23-69
98                <category android:name="android.intent.category.BROWSABLE" />
98-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:80:13-74
98-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:80:23-71
99
100                <data android:scheme="tel" />
100-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:13-42
100-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:68:19-39
101            </intent-filter>
102        </activity> <!-- InCallService - REQUIRED for default dialer -->
103        <service
103-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:86:7-99:17
104            android:name="com.videoringtoneapp.VideoRingtoneInCallService"
104-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:87:9-51
105            android:exported="true"
105-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:89:9-32
106            android:permission="android.permission.BIND_INCALL_SERVICE" >
106-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:88:9-68
107            <intent-filter>
107-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:90:9-92:25
108                <action android:name="android.telecom.InCallService" />
108-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:91:11-66
108-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:91:19-63
109            </intent-filter>
110
111            <meta-data
111-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:93:9-95:34
112                android:name="android.telecom.INCLUDE_EXTERNAL_CALLS"
112-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:94:11-64
113                android:value="true" />
113-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:95:11-31
114            <meta-data
114-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:96:9-98:34
115                android:name="android.telecom.INCLUDE_SELF_MANAGED_CALLS"
115-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:97:11-68
116                android:value="true" />
116-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:98:11-31
117        </service> <!-- Video Ringtone Service -->
118        <service
118-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:102:7-106:53
119            android:name="com.videoringtoneapp.VideoRingtoneService"
119-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:103:9-45
120            android:enabled="true"
120-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:104:9-31
121            android:exported="false"
121-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:105:9-33
122            android:foregroundServiceType="phoneCall" /> <!-- Call State Receiver -->
122-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:106:9-50
123        <receiver
123-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:109:7-116:18
124            android:name="com.videoringtoneapp.CallStateReceiver"
124-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:110:9-42
125            android:enabled="true"
125-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:111:9-31
126            android:exported="true" >
126-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:112:9-32
127            <intent-filter android:priority="1000" >
127-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:113:9-115:25
127-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:113:24-47
128                <action android:name="android.intent.action.PHONE_STATE" />
128-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:114:11-70
128-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:114:19-67
129            </intent-filter>
130        </receiver> <!-- Video Ringtone Activity -->
131        <activity
131-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:119:7-126:36
132            android:name="com.videoringtoneapp.VideoRingtoneActivity"
132-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:120:9-46
133            android:excludeFromRecents="true"
133-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:123:9-42
134            android:exported="false"
134-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:126:9-33
135            android:launchMode="singleTop"
135-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:122:9-39
136            android:showOnLockScreen="true"
136-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:124:9-40
137            android:theme="@style/VideoRingtoneTheme"
137-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:121:9-50
138            android:turnScreenOn="true" />
138-->H:\Coding\VibeCoding\video-ringtone\VideoRingtoneApp\android\app\src\main\AndroidManifest.xml:125:9-36
139        <activity
139-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
140            android:name="com.facebook.react.devsupport.DevSettingsActivity"
140-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
141            android:exported="false" />
141-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cb416b8178cb0e7f140959230f7f0bf\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
142
143        <provider
143-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
144            android:name="androidx.startup.InitializationProvider"
144-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
145            android:authorities="com.videoringtoneapp.androidx-startup"
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
146            android:exported="false" >
146-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
147            <meta-data
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
148                android:name="androidx.emoji2.text.EmojiCompatInitializer"
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
149                android:value="androidx.startup" />
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5891401dca9fb3785a05e54c3e73c8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
150            <meta-data
150-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
151                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
151-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
152                android:value="androidx.startup" />
152-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e8dd4b04eb1d3616e7f1aefde3440fe1\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
153            <meta-data
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
154                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
155                android:value="androidx.startup" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
156        </provider>
157
158        <receiver
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
159            android:name="androidx.profileinstaller.ProfileInstallReceiver"
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
160            android:directBootAware="false"
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
161            android:enabled="true"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
162            android:exported="true"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
163            android:permission="android.permission.DUMP" >
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
164            <intent-filter>
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
165                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
166            </intent-filter>
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
168                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
171                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
172            </intent-filter>
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
174                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ece3cf32f32a1af3867955959506357\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
175            </intent-filter>
176        </receiver>
177
178        <meta-data
178-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
179            android:name="com.facebook.soloader.enabled"
179-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
180            android:value="false" />
180-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0cf20206953c7b50bf34a969af20a7a\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
181    </application>
182
183</manifest>
