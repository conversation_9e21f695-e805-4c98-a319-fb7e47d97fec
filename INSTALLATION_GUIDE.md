# Video Ringtone App - Installation & Setup Guide

## 📱 APK Files Available

### 1. **VideoRingtoneApp-FIXED-release.apk** (Recommended) ⭐ NEW
- **Size**: 50.68 MB
- **Type**: Release build (optimized)
- **Best for**: Production use, better performance
- **JavaScript Bundle**: Included
- **Fixes**: ✅ Storage permissions for Android 13+ ✅ Vivo device support ✅ Enhanced default dialer

### 2. **VideoRingtoneApp-FIXED-debug.apk** ⭐ NEW
- **Size**: 116.66 MB
- **Type**: Debug build
- **Best for**: Development/testing
- **JavaScript Bundle**: Included
- **Fixes**: ✅ Storage permissions for Android 13+ ✅ Vivo device support ✅ Enhanced default dialer

### Legacy Files (Previous Versions)
- **VideoRingtoneApp-FINAL-release.apk** - 50.68 MB (older version)
- **VideoRingtoneApp-FINAL-debug.apk** - 116.63 MB (older version)

## 🚀 Installation Steps

### Step 1: Enable Unknown Sources
1. Go to **Settings** > **Security** (or **Privacy**)
2. Enable **"Install from Unknown Sources"** or **"Allow from this source"**
3. Or when installing, Android will prompt you to allow installation

### Step 2: Install the APK
1. Transfer the APK file to your Android device
2. Tap on the APK file to install
3. Follow the installation prompts
4. Tap **"Install"** when prompted

### Step 3: Initial Setup
1. **Open the app** after installation
2. **Grant all permissions** when prompted:
   - Phone State Access
   - Storage Access
   - System Alert Window
   - Call Management
3. **Set as Default Dialer** (very important!)
4. **Test the app** using the built-in test function

## 🎯 How to Use

### Setting Up Video Ringtone
1. **Grant Permissions**: Use the app interface to grant all required permissions
2. **Select Video**: Tap "Select Video File" to choose a video from your device
3. **Confirm Selection**: Review video details and confirm to set as ringtone
4. **Test**: Use "Test Video Ringtone" to verify it works

### Video Requirements
- **Formats**: MP4, 3GP, WebM, MKV, AVI, MOV
- **Size**: Maximum 50MB
- **Duration**: 10-30 seconds recommended (will loop)
- **Resolution**: 1080p or lower for best performance

## 🔧 Troubleshooting

### "Unable to load script" Error
✅ **FIXED**: Both APK files now include the JavaScript bundle

### App Crashes on Startup
- Make sure you're using Android 7.0 (API 24) or higher
- Try the release APK instead of debug APK
- Clear app data and restart

### Storage Permission Issues (Android 13+)
**For Android 13 and newer devices:**
1. The app now uses `READ_MEDIA_VIDEO` permission instead of `READ_EXTERNAL_STORAGE`
2. If permission keeps asking after granting:
   - Go to **Settings** > **Apps** > **Video Ringtone App** > **Permissions**
   - Enable **Photos and videos** permission
   - Make sure it's set to "Allow" not "Ask every time"

### Vivo Device Specific Issues (iQOO Neo 7 Pro and similar)
**Vivo devices have additional permission restrictions:**

#### Storage Permission Fix:
1. Open **Settings** > **Apps** > **Video Ringtone App**
2. Go to **Permissions** > **Storage** or **Photos and videos**
3. Set to **Allow** (not "Ask every time")
4. Also check **i Manager** app if available:
   - Open **i Manager** > **App permissions**
   - Find **Video Ringtone App** > **Storage** > **Allow**

#### Default Dialer Fix:
1. Go to **Settings** > **Apps** > **Default apps** > **Phone app**
2. Select **Video Ringtone App** as default
3. If not working, try:
   - **Settings** > **Apps** > **Video Ringtone App** > **Set as default**
   - Look for **"Open by default"** or **"Default apps"** section
4. Alternative: **Settings** > **System** > **Default apps** > **Phone**

#### Additional Vivo Steps:
1. **Disable Battery Optimization:**
   - **Settings** > **Battery** > **Background app refresh**
   - Find **Video Ringtone App** > Set to **No restrictions**
2. **Enable Auto-start:**
   - **Settings** > **Apps** > **Video Ringtone App** > **Battery**
   - Enable **Auto-start** and **Background activity**
3. **System Alert Window:**
   - **Settings** > **Apps** > **Video Ringtone App** > **Permissions**
   - Enable **Display over other apps**

### Video Ringtone Not Playing
1. Ensure all permissions are granted (especially storage/media permissions)
2. Set the app as default dialer
3. Test with the built-in test function first
4. Try a different video file (MP4 recommended)
5. For Vivo devices: Check i Manager permissions

### Call Detection Not Working
1. **Most Important**: Set app as default dialer
2. Grant phone state permissions
3. Enable system alert window permission
4. For Vivo devices: Disable battery optimization and enable auto-start
5. Restart the app after granting permissions

## 📋 Required Permissions

The app needs these permissions to function:

- ✅ **READ_PHONE_STATE**: Detect incoming calls
- ✅ **CALL_PHONE**: Make calls
- ✅ **ANSWER_PHONE_CALLS**: Answer/decline calls
- ✅ **SYSTEM_ALERT_WINDOW**: Display over other apps
- ✅ **READ_EXTERNAL_STORAGE**: Access video files (Android 12 and below)
- ✅ **READ_MEDIA_VIDEO**: Access video files (Android 13+)
- ✅ **VIBRATE**: Vibration during calls
- ✅ **WAKE_LOCK**: Keep screen on during calls

### Permission Notes:
- **Android 13+**: Uses `READ_MEDIA_VIDEO` instead of `READ_EXTERNAL_STORAGE`
- **Vivo Devices**: May require additional steps in i Manager app
- **Default Dialer**: Essential for call detection and management

## 🎨 Features

### ✅ Implemented Features
- 🎥 Full-screen video ringtone playback
- 📁 Video file selection from device storage
- 📞 Answer/Decline call buttons
- 🔔 Audio ringtone backup
- 📳 Vibration support
- 🔒 Lock screen compatibility
- 🎛️ Permission management interface
- 🧪 Built-in testing functionality

### 📱 Supported Android Versions
- **Target**: Android 15 (API 35)
- **Minimum**: Android 7.0 (API 24)
- **Tested**: Android 8.0+ recommended

## 💡 Tips for Best Experience

1. **Use MP4 format** for best compatibility
2. **Keep videos under 30 seconds** for optimal performance
3. **Test thoroughly** before relying on the app
4. **Grant all permissions** for full functionality
5. **Set as default dialer** - this is crucial!

## 🆘 Support

If you encounter issues:
1. Check that all permissions are granted
2. Ensure the app is set as default dialer
3. Try the test function first
4. Use the release APK for better stability
5. For Vivo devices: Follow the Vivo-specific steps above
6. Restart your device if problems persist

## 📱 Vivo Device Quick Fix Guide

**For iQOO Neo 7 Pro and other Vivo devices:**

### Step 1: Storage Permission
```
Settings > Apps > Video Ringtone App > Permissions > Photos and videos > Allow
```

### Step 2: Default Dialer
```
Settings > Apps > Default apps > Phone app > Video Ringtone App
```

### Step 3: Battery Optimization
```
Settings > Battery > Background app refresh > Video Ringtone App > No restrictions
```

### Step 4: Auto-start
```
Settings > Apps > Video Ringtone App > Battery > Enable Auto-start
```

### Step 5: i Manager (if available)
```
i Manager > App permissions > Video Ringtone App > Storage > Allow
```

### Step 6: Test
Use the app's built-in test function to verify everything works.

## 🔄 Updates

To update the app:
1. Uninstall the old version
2. Install the new APK
3. Re-grant all permissions
4. Re-select your video ringtone

---

**Enjoy your personalized video ringtones! 🎉**
